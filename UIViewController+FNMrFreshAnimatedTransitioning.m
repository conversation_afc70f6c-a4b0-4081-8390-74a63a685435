//
//  UIViewController+FNMrFreshAnimatedTransitioning.m
//  FNFresh
//
//  Created by yong<PERSON><PERSON> on 2017/4/1.
//  Copyright © 2017年 FeiNiu. All rights reserved.
//

#import "UIViewController+FNMrFreshAnimatedTransitioning.h"
#import "FNFreshMrFreshCustomAnimatedTransitioning.h"
#import "FNBaseNavigationController.h"
#import "UIViewController+FNNavigationBarHidden.h"
#import <objc/runtime.h>

@implementation UIViewController (FNMrFreshAnimatedTransitioning)

- (void)fn_presentViewController:(UIViewController *)viewController customAnimateType:(FNFreshMrFreshCustomAnimateType)customAnimateType needNavigation:(BOOL)need viewSize:(CGSize)viewSize duration:(NSTimeInterval)duration alpha:(CGFloat)alpha handle:(void (^)(void))handle
{
    UIViewController *vc = viewController;
    if (need) {
        FNBaseNavigationController *nav = [[FNBaseNavigationController alloc] initWithRootViewController:viewController];
        nav.modalPresentationStyle = UIModalPresentationFullScreen;
        vc.fnPreferNavigationBarHidden = YES;
        vc = nav;
    }
    
    FNFreshMrFreshCustomAnimatedTransitioning *customAnimatedTransitioning = [[FNFreshMrFreshCustomAnimatedTransitioning alloc] init];
    customAnimatedTransitioning.viewSize = viewSize;
    customAnimatedTransitioning.alpha = alpha;
    customAnimatedTransitioning.duration = duration;
    customAnimatedTransitioning.customAnimateType = customAnimateType;
    customAnimatedTransitioning.handle = handle;
    vc.transitioningDelegate = (id<UIViewControllerTransitioningDelegate>)customAnimatedTransitioning;
    switch (customAnimateType) {
        case FNFreshMrFreshCustomAnimateTypeNone:
        case FNFreshMrFreshCustomAnimateTypeFromBottom:
        case FNFreshMrFreshCustomAnimateTypeFromCenterKickBack:
        case FNFreshMrFreshCustomAnimateTypeFadeInFadeOut:
            vc.modalPresentationStyle = UIModalPresentationCustom;
            break;
        case FNFreshMrFreshCustomAnimateTypeSearch:
            break;
    }
    if (self.presentedViewController) {
        UIViewController *vc = self.presentedViewController;
        [vc dismissViewControllerAnimated:YES completion:nil];
    }
    [self presentViewController:vc animated:YES completion:nil];
    objc_setAssociatedObject(vc, _cmd, customAnimatedTransitioning, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (void)fn_presentViewController:(UIViewController *)viewController customAnimateType:(FNFreshMrFreshCustomAnimateType)customAnimateType viewSize:(CGSize)viewSize duration:(NSTimeInterval)duration alpha:(CGFloat)alpha handle:(void (^)(void))handle
{
    [self fn_presentViewController:viewController customAnimateType:customAnimateType needNavigation:NO viewSize:viewSize duration:duration alpha:alpha handle:handle];
}

- (void)fn_presentViewController:(UIViewController *)viewController customAnimateType:(FNFreshMrFreshCustomAnimateType)customAnimateType duration:(NSTimeInterval)duration alpha:(CGFloat)alpha handle:(void (^)(void))handle
{
    [self fn_presentViewController:viewController customAnimateType:customAnimateType needNavigation:NO viewSize:[UIScreen mainScreen].bounds.size duration:duration alpha:alpha handle:handle];
}

- (void)fn_presentViewController:(UIViewController *)viewController customAnimateType:(FNFreshMrFreshCustomAnimateType)customAnimateType needNavigation:(BOOL)need duration:(NSTimeInterval)duration alpha:(CGFloat)alpha handle:(void (^)(void))handle
{
    [self fn_presentViewController:viewController customAnimateType:customAnimateType needNavigation:need viewSize:[UIScreen mainScreen].bounds.size duration:duration alpha:alpha handle:handle];
}

#pragma mark - setter && getter

- (void)setHasPresentedViewController:(BOOL)hasPresentedViewController {
    
    objc_setAssociatedObject(self, @selector(isHasPresentedViewController), @(hasPresentedViewController), OBJC_ASSOCIATION_RETAIN);
}

- (BOOL)isHasPresentedViewController {
    
    return [objc_getAssociatedObject(self, _cmd) boolValue];
}

@end
