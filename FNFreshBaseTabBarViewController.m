//
//  FNFreshBaseTabBarViewController.m
//  FNFresh
//
//  Created by EchoJuneMac-Pro on 2020/3/3.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import "FNFreshBaseTabBarViewController.h"
#import "UIFont+FNFont.h"
#import "UIViewController+FNNavigationBarHidden.h"
#import "UITabBar+Badge.h"
#import "FNFreshMrFreshService.h"
#import "FNPrivacyResponseModel.h"
#import "FNFreshTabBarController.h"
#import "FNFreshMrFreshResponseModel.h"
#import "FNFreshMrFreshConstantCacheHandler.h"
#import "FNFreshMrFreshViewController.h"

@interface FNFreshBaseTabBarViewController () <UITabBarControllerDelegate>

@end

@implementation FNFreshBaseTabBarViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.tabBar.translucent = NO;
    self.tabBar.offect = UIOffsetMake(-2, -3);
    self.automaticallyAdjustsScrollViewInsets = NO;
    self.fnPreferNavigationBarHidden = YES;
    
    if (@available(iOS 15.0, *)) { // 解决IOS15的系统上， 底部tabar背景色透明，且item设置文本颜色失效等问题
        UITabBarItemAppearance *itemAppearance = [[UITabBarItemAppearance alloc] init];
        itemAppearance.normal.titleTextAttributes = @{NSFontAttributeName:[UIFont fn_FontWithFontKey:kFC_E],NSForegroundColorAttributeName:[UIColor fn_colorWithColorKey:kFN333333]};
        itemAppearance.selected.titleTextAttributes = @{NSFontAttributeName:[UIFont fn_FontWithFontKey:kFC_E],NSForegroundColorAttributeName:[UIColor fn_colorWithColorKey:kFNThemeKey]};
        
        // 设置文字与图片距离
        itemAppearance.normal.titlePositionAdjustment = UIOffsetMake(0, -3);
        itemAppearance.selected.titlePositionAdjustment = UIOffsetMake(0, -3);
        
        UITabBarAppearance *barAppearance = [[UITabBarAppearance alloc] init];
        barAppearance.backgroundColor = [UIColor whiteColor];
        barAppearance.inlineLayoutAppearance = itemAppearance;
        barAppearance.stackedLayoutAppearance = itemAppearance;
        barAppearance.shadowImage = [UIImage new];
        barAppearance.shadowColor = [UIColor clearColor];
        barAppearance.compactInlineLayoutAppearance = itemAppearance;
        self.tabBar.scrollEdgeAppearance = barAppearance;
        self.tabBar.standardAppearance = barAppearance;
    }
    
    // 改变tabbar 线条颜色
    [self changeTabBarLineColor:UIColor.whiteColor];
    
    [self requestPrivateRule];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
}

// 用户隐私协议
- (void)requestPrivateRule {
    [FNFreshMrFreshService requestPrivateRuleWithParameter:nil success:^(FNPrivacyResponseModel *responseObject, BOOL isCache) {
        [[FNFreshUtils shareInstance] setPrivacyVersion:responseObject.privacyVersion content:responseObject.privacyRuleBody andBottom:responseObject.privacyRuleBottom];
    } failure:^(id responseObject, NSError *error) {
        
    }];
}

#pragma mark - Base functions
/**
 改变tabBar 线条颜色
 */
- (void)changeTabBarLineColor:(UIColor *)color {
    [self removeTabBarTopLine];
    
    if (@available(iOS 13.0, *)) {
        UIView *blankView = [[UIView alloc] initWithFrame:CGRectMake(0, -0.5, UIScreen.mainScreen.bounds.size.width, 0.5)];
        blankView.backgroundColor = color;
        blankView.tag = 1001;
        [self.tabBar addSubview:blankView];
    } else {
        CGRect rect = CGRectMake(0, 0, Main_Screen_Width, 0.5);
        fn_UIGraphicsBeginImageContext(rect.size);
        CGContextRef context = UIGraphicsGetCurrentContext();
        CGContextSetFillColorWithColor(context, color.CGColor);
        CGContextFillRect(context, rect);
        UIImage *img = UIGraphicsGetImageFromCurrentImageContext();
        UIGraphicsEndImageContext();
        [self.tabBar setShadowImage:img];
        [self.tabBar setBackgroundImage:[[UIImage alloc]init]];
    }
    self.tabBar.layer.shadowColor = UIColor.blackColor.CGColor;
    self.tabBar.layer.shadowOffset = CGSizeMake(0, -5);
    self.tabBar.layer.shadowOpacity = 0.05;
    self.tabBar.layer.shadowRadius = 5;
    UIBezierPath *path = [UIBezierPath bezierPathWithRect:self.tabBar.bounds];
    self.tabBar.layer.shadowPath = path.CGPath;
}

//在没有配置tabbar的背景图片的时候，要把tabbar顶部的细线移除
- (void)removeTabBarTopLine {
    if (@available(iOS 13.0, *)) {
        // iOS 13 及以上版本，移除之前添加的细线视图
        UIView *lineView = [self.tabBar viewWithTag:1001];
        if (lineView) {
            [lineView removeFromSuperview];
        }
    } else {
        // iOS 13 以下版本，清除 shadowImage
        [self.tabBar setShadowImage:[UIImage new]];
        [self.tabBar setBackgroundImage:[[UIImage alloc]init]];
    }
    self.tabBar.layer.shadowColor = nil;
    self.tabBar.layer.shadowOpacity = 0;
    self.tabBar.layer.shadowRadius = 0;
    self.tabBar.layer.shadowPath = nil;
}

/**
 添加子vc
 */
- (void)FNAddChildViewController:(UIViewController *)controller 
                           title:(NSString *)title
                           image:(UIImage *)image
                   selectedImage:(UIImage *)selectedImage {
    
    UIViewController *chilrdVC =  [self FNConfigureChildViewController:controller title:title image:image selectedImage:selectedImage];
    [self addChildViewController:chilrdVC];
}

- (UIViewController *)FNConfigureChildViewController:(UIViewController *)controller 
                                               title:(NSString *)title
                                               image:(UIImage *)image
                                       selectedImage:(UIImage *)selectedImage {
    controller.tabBarItem.title = title;
    
    [controller.tabBarItem setTitleTextAttributes:@{NSFontAttributeName:[UIFont fn_FontWithFontKey:kFC_E],NSForegroundColorAttributeName:[UIColor fn_colorWithColorKey:kFN333333]} forState:UIControlStateNormal];
    [controller.tabBarItem setTitleTextAttributes:@{NSFontAttributeName:[UIFont fn_FontWithFontKey:kFC_E],NSForegroundColorAttributeName:[UIColor fn_colorWithColorKey:kFNThemeKey]} forState:UIControlStateSelected];
    controller.tabBarItem.imageInsets = UIEdgeInsetsMake(-1, 0, 1, 0);
    [controller.tabBarItem setTitlePositionAdjustment:UIOffsetMake(0, -3)];
    UIImage *originalImage = [image imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    controller.tabBarItem.image = originalImage;
    controller.tabBarItem.selectedImage = [selectedImage imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    return controller;
}

/**
 跳转
 */
- (void)pushViewController:(UIViewController *)viewController animated:(BOOL)animated {
    if (!viewController) {
        NSLog(@"[TabBar] Warning: Attempting to push nil viewController");
        return;
    }

    if (self.navigationController) {
        // 检查是否有present的视图控制器
        if (self.navigationController.presentedViewController != nil) {
            NSLog(@"[TabBar] Warning: Cannot push while navigationController has presented view controller: %@",
                  NSStringFromClass([self.navigationController.presentedViewController class]));

            // 尝试延迟执行，等待present的视图控制器dismiss
            __weak typeof(self) weakSelf = self;
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                if (weakSelf.navigationController.presentedViewController == nil) {
                    NSLog(@"[TabBar] Retrying push after presented view controller dismissed");
                    [weakSelf.navigationController pushViewController:viewController animated:animated];
                } else {
                    NSLog(@"[TabBar] Still has presented view controller, push failed");
                }
            });
            return;
        }

        // 检查navigationController是否处于正常状态
        if (self.navigationController.isBeingPresented || self.navigationController.isBeingDismissed) {
            NSLog(@"[TabBar] Warning: Cannot push while navigationController is in transition");
            return;
        }

        NSLog(@"[TabBar] Pushing view controller: %@", NSStringFromClass([viewController class]));
        [self.navigationController pushViewController:viewController animated:animated];
    } else {
        NSLog(@"[TabBar] Warning: navigationController is nil, cannot push viewController: %@", viewController);
    }
}

- (void)backToHomeViewAnimated:(BOOL)flag {
    [self.navigationController popToRootViewControllerAnimated:flag];
    [self.navigationController dismissViewControllerAnimated:flag completion:nil];
    [self setSelectedIndex:0];
}

- (void)backToOnlineHomeViewAnimated:(BOOL)flag WithH5Link:(NSString *)link {
    [self backToHomeViewAnimated:flag];
    [[FNFreshTabBarController shareInstance].homeVC addH5WebViewWithLinkUrl:link];
}

- (void)initializeShopCartTabIcon {
    // implementation some code
}

@end

