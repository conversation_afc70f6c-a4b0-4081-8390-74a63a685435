//
//  FNFreshMrFreshCollectionViewFlowLayout.h
//  FNFresh
//
//  Created by yong<PERSON><PERSON> on 2017/2/21.
//  Copyright © 2017年 FeiNiu. All rights reserved.
//

#import <UIKit/UIKit.h>

static inline CGFloat topBarHeight() {
    
    return [UIApplication sharedApplication].statusBarFrame.size.height + 44;
}

@protocol FNMrFreshCollectionViewDelegateFlowLayout <UICollectionViewDelegateFlowLayout>

@optional

- (UIColor *)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout backgroundColorForSection:(NSInteger)section;

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout spaceAtSection:(NSInteger)section;

- (NSString *)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout bgImgUrlForSection:(NSInteger)section;

@end


@interface FNFreshMrFreshCollectionViewFlowLayout : UICollectionViewFlowLayout

@end
