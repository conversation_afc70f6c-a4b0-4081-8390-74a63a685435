//
//  FNFreshMrFreshFeedsGoodsViewController.h
//  FNFresh
//
//  Created by wang<PERSON> on 2019/5/17.
//  Copyright © 2019 FeiNiu. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "FNFreshMrFreshResponseModel.h"

@class FNFreshMrFreshGoodsList;

@protocol FNFreshMrFreshFeedsGoodsGestureDelegate <NSObject>

- (void)pageViewControllerLeaveTop:(BOOL)isLeaveTop;
- (void)pageViewControllerDidEndScroll:(BOOL)isEndScroll;

@end

@protocol FNFreshMrFreshFeedsGoodsDelegate <NSObject>

- (void)fn_freshMrFreshFeedsGoodsAddToCartWithDataModel:(FNFreshMrFreshGoodsList *)dataModel andImageView:(UIImageView *)imageView andCell:(UICollectionViewCell *)cell;

@end

@interface FNFreshMrFreshFeedsGoodsViewController : UICollectionViewController

@property (weak, nonatomic) id<FNFreshMrFreshFeedsGoodsDelegate> delegate;
@property (weak, nonatomic) id<FNFreshMrFreshFeedsGoodsGestureDelegate> gestureDelegate;

@property (strong, nonatomic) FNMrFreshContentModel *dataModel;
@property (assign, nonatomic) NSUInteger index;

- (void)makePageViewControllerScroll:(BOOL)canScroll;
- (void)makePageViewControllerScrollToTop;
- (void)makeScrollTargetOffset:(CGPoint)contentOffset;
- (void)setNegativeFeedbackDismiss;

@end
