//
//  FNFreshMrFreshCollectionViewFlowLayout.m
//  FNFresh
//
//  Created by yong<PERSON><PERSON> on 2017/2/21.
//  Copyright © 2017年 FeiNiu. All rights reserved.
//

#import "FNFreshMrFreshCollectionViewFlowLayout.h"
#import "FNFreshMrFreshDecorationView.h"
#import "FNFreshMrFreshDecorationLayoutAttributes.h"

static inline BOOL fequal(CGFloat a, CGFloat b) {
    
    return fabs(a - b) < 0.001;
}

static inline CGFloat fRound(CGFloat a) {
    
    return round(a *1000) / 1000;
}

static NSString *const kMrFreshDecorationView = @"kMrFreshDecorationView";

@interface FNFreshMrFreshCollectionViewFlowLayout ()

@property (strong, nonatomic) NSMutableArray *sectionRectArray;
@property (strong, nonatomic) NSMutableArray *itemAttributesArray;
@property (strong, nonatomic) NSMutableArray *headerViewAttributesArray;
@property (strong, nonatomic) NSMutableArray *footerViewAttributesArray;
@property (strong, nonatomic) NSMutableArray *decorationViewAttributesArray;

@property (weak, nonatomic) id<FNMrFreshCollectionViewDelegateFlowLayout> delegate;

@end

@implementation FNFreshMrFreshCollectionViewFlowLayout

#pragma mark - override super

- (instancetype)initWithCoder:(NSCoder *)aDecoder {
    
    if (self = [super initWithCoder:aDecoder]) {
        
        [self registerClass:[FNFreshMrFreshDecorationView class] forDecorationViewOfKind:
         kMrFreshDecorationView];
    }
    return self;
}

- (void)prepareLayout {
    
    [super prepareLayout];
    self.minimumLineSpacing = 0.5;
    self.minimumInteritemSpacing = 0.5;
    NSInteger sections = self.collectionView.numberOfSections;
    for (NSUInteger i = 0; i < sections; i ++) {
        
        NSMutableArray *array = [NSMutableArray array];
        [self.itemAttributesArray addObject:array];
        [self calculateAttributesAtSection:i];
    }
}

- (CGSize)collectionViewContentSize {
    
    CGSize contentSize = CGSizeMake(CGRectGetWidth(self.collectionView.bounds), 
                                    CGRectGetMaxY([self.sectionRectArray.lastObject CGRectValue]));
    return contentSize;
}

- (UICollectionViewLayoutAttributes *)layoutAttributesForItemAtIndexPath:(NSIndexPath *)indexPath {
    
    return self.itemAttributesArray[indexPath.section][indexPath.row];
}

- (UICollectionViewLayoutAttributes *)layoutAttributesForSupplementaryViewOfKind:(NSString *)elementKind 
                                                                     atIndexPath:(NSIndexPath *)indexPath {
    
    return ([elementKind isEqualToString:UICollectionElementKindSectionHeader] ? 
            self.headerViewAttributesArray : self.footerViewAttributesArray)[indexPath.section];
}

- (UICollectionViewLayoutAttributes *)layoutAttributesForDecorationViewOfKind:(NSString *)elementKind 
                                                                  atIndexPath:(NSIndexPath *)indexPath {
    
    return self.decorationViewAttributesArray[indexPath.section];
}

- (NSArray<UICollectionViewLayoutAttributes *> *)layoutAttributesForElementsInRect:(CGRect)rect {
    
    NSMutableArray *mutableArray = [NSMutableArray array];
    NSIndexSet *sections = [self visiableSectionInRect:rect];
    
    [sections enumerateIndexesUsingBlock:^(NSUInteger idx, BOOL * _Nonnull stop) {
        
        NSArray *array = [self.itemAttributesArray safeObjectAtIndex:idx];
        for (UICollectionViewLayoutAttributes *itemAttributes in array) {
            if (itemAttributes && CGRectIntersectsRect(itemAttributes.frame, rect)) {
                [mutableArray addObject:itemAttributes];
            }
        }
        
        UICollectionViewLayoutAttributes *headerAttributes = 
        [self.headerViewAttributesArray safeObjectAtIndex:idx];
        if (headerAttributes &&  CGRectIntersectsRect(headerAttributes.frame, rect)) {
            
            [mutableArray addObject:headerAttributes];
        }
        
        UICollectionViewLayoutAttributes *footerAttributes = 
        [self.footerViewAttributesArray safeObjectAtIndex:idx];
        if (footerAttributes && CGRectIntersectsRect(footerAttributes.frame, rect)) {
            
            [mutableArray addObject:footerAttributes];
        }
        
        FNFreshMrFreshDecorationLayoutAttributes *decorationAttributs = 
        [self.decorationViewAttributesArray safeObjectAtIndex:idx];
        if (decorationAttributs && CGRectIntersectsRect(decorationAttributs.frame, rect)) {
            
            [mutableArray addObject:decorationAttributs];
        }
    }];
    
    return mutableArray;
}

- (BOOL)shouldInvalidateLayoutForBoundsChange:(CGRect)newBounds {
    
    return CGRectGetWidth(self.collectionView.bounds) != CGRectGetWidth(newBounds);
}

- (void)invalidateLayout {
    
    [super invalidateLayout];
    self.sectionRectArray = nil;
    self.itemAttributesArray = nil;
    self.headerViewAttributesArray = nil;
    self.footerViewAttributesArray = nil;
    self.decorationViewAttributesArray = nil;
}

#pragma mark - pritave methods

- (void)calculateAttributesAtSection:(NSUInteger)section {
    
    CGRect lastSectionRect = [self rectForSection:section - 1];
    CGFloat sectionSpace = [self spaceAtSection:section];
    CGFloat currentSectionRectY = CGRectGetMaxY(lastSectionRect) + sectionSpace;
    
    CGFloat headerViewHeight = 0;
    //headerView
    if ([self.delegate respondsToSelector:@selector(collectionView:layout:referenceSizeForHeaderInSection:)]) {
        
        [self updateheaderViewAttributesArray:section and:&headerViewHeight sectionY:currentSectionRectY];
    }
    
    //item
    UIEdgeInsets sectionInset = [self sectionInsetAtSection:section];
    CGFloat contentRectX = sectionInset.left;
    CGFloat contentRectY = currentSectionRectY + headerViewHeight + sectionInset.top;
    
    CGFloat columnWidth =
    CGRectGetWidth(self.collectionView.bounds)- sectionInset.left - sectionInset.right;
    
    NSMutableArray *columnRectArray = [self throughTheItems:section
                                                   contentX:contentRectX
                                                   contentY:contentRectY
                                                columnWidth:columnWidth];
    
    CGFloat contentRectHeight = CGRectGetHeight([columnRectArray.lastObject CGRectValue]);
    
    //footerView
    CGFloat footerViewHeight = 0;
    if ([self.delegate respondsToSelector:@selector(collectionView:layout:referenceSizeForFooterInSection:)]) {
        
        [self updatefooterViewAttributesArray:section 
                                          and:&footerViewHeight
                                     contentH:contentRectHeight
                                     contentY:contentRectY];
        
    }
    
    //sectionRect
    CGRect currentSectionRect = CGRectMake(0, currentSectionRectY, 
                                           CGRectGetWidth(self.collectionView.bounds),
                                           headerViewHeight + sectionInset.top +
                                           contentRectHeight + sectionInset.bottom +
                                           footerViewHeight);
    [self updateSectionRectArray:section and:currentSectionRect space:sectionSpace];
    
    //decorationView
    //    [self updateDecorationViewAttributesArray:section and:currentSectionRect];
}

- (void)updateheaderViewAttributesArray:(NSUInteger)section 
                                    and:(CGFloat *)headerViewHeight
                               sectionY:(CGFloat)currentSectionRectY {
    NSIndexPath *sectionIndexPath = [NSIndexPath indexPathForRow:0 inSection:section];
    CGSize referenceSize = [self.delegate collectionView:self.collectionView 
                                                  layout:self
                         referenceSizeForHeaderInSection:section];
    referenceSize = CGSizeMake(fRound(referenceSize.width), fRound(referenceSize.height));
    *headerViewHeight = referenceSize.height;
    
    UICollectionViewLayoutAttributes *sectionHeaderViewAttribute =
    [UICollectionViewLayoutAttributes layoutAttributesForSupplementaryViewOfKind:UICollectionElementKindSectionHeader
                                                                   withIndexPath:sectionIndexPath];
    
    CGRect headerViewFrame = (CGRect){{0, currentSectionRectY}, referenceSize};
    sectionHeaderViewAttribute.frame = headerViewFrame;
    if ([self.headerViewAttributesArray safeObjectAtIndex:section]) {
        
        [self.headerViewAttributesArray replaceObjectAtIndex:section 
                                                  withObject:sectionHeaderViewAttribute];
        [self.itemAttributesArray[section] removeAllObjects];
    } else {
        
        [self.headerViewAttributesArray addObject:sectionHeaderViewAttribute];
    }
}

- (NSMutableArray *)throughTheItems:(NSUInteger)section 
                           contentX:(CGFloat)contentRectX
                           contentY:(CGFloat)contentRectY
                        columnWidth:(CGFloat)columnWidth {
    CGFloat lineSpace = [self minimumLineSpacingAtSection:section];
    CGFloat itemSpace = [self minimumInteritemSpacingAtSection:section];
    CGRect columnRect = CGRectMake(contentRectX, contentRectY, columnWidth, 0);
    NSMutableArray *columnRectArray = [NSMutableArray array];
    [columnRectArray addObject:[NSValue valueWithCGRect:columnRect]];
    NSInteger items = [self.collectionView numberOfItemsInSection:section];
    for (NSInteger i = 0; i < items; i ++) {
        
        NSIndexPath *itemIndexPath = [NSIndexPath indexPathForRow:i inSection:section];
        CGSize itemSize = [self itemSizeAtIndexPath:itemIndexPath];
        CGPoint itemOrigin = CGPointZero;
        
        UICollectionViewLayoutAttributes *itemAttributes =
        [UICollectionViewLayoutAttributes layoutAttributesForCellWithIndexPath:itemIndexPath];
        
        if (itemSize.width >= columnWidth) {
            
            CGFloat originY = CGRectGetMaxY([columnRectArray.lastObject CGRectValue]);
            CGFloat centerY = (originY == contentRectY ? originY : originY + lineSpace) + itemSize.height/2;
            itemAttributes.center = CGPointMake(SCREEN_WIDTH/2, centerY);
            itemAttributes.size = itemSize;
            [self.itemAttributesArray[section] addObject:itemAttributes];
            [columnRectArray removeAllObjects];
            CGRect colRect = CGRectMake(contentRectX, contentRectY, columnWidth, 
                                        CGRectGetMaxY(itemAttributes.frame) - contentRectY);
            [columnRectArray addObject:[NSValue valueWithCGRect:colRect]];
            continue;
        }
        
        for (NSInteger j = 0; j < columnRectArray.count; j ++) {
            
            CGRect rect = [[columnRectArray safeObjectAtIndex:j] CGRectValue];
            CGFloat needWidth = rect.origin.x == contentRectX ? itemSize.width : itemSize.width + itemSpace;
            CGFloat offect = needWidth - rect.size.width;
            if (offect > 0 && offect < 0.01) {
                
                itemSize.width -= offect;
            }
            if (rect.size.width >= (rect.origin.x == contentRectX ? itemSize.width : itemSize.width + itemSpace)) {
                
                itemOrigin.x = rect.origin.x == contentRectX ? rect.origin.x : rect.origin.x + itemSpace;
                CGFloat originY = CGRectGetMaxY(rect);
                itemOrigin.y = originY == contentRectY ? originY : originY + lineSpace;
                break;
            }
        }
        itemAttributes.frame = (CGRect){itemOrigin, itemSize};
        itemAttributes.zIndex = 1;
        [self.itemAttributesArray[section] addObject:itemAttributes];
        
        CGFloat width = itemSize.width;
        if (itemOrigin.x > contentRectX) {
            
            width += itemSpace;
        }
        CGRect tempRect = CGRectMake(itemOrigin.x == contentRectX ? itemOrigin.x : itemOrigin.x - itemSpace, 
                                     contentRectY,
                                     width,
                                     itemOrigin.y - contentRectY + itemSize.height);
        
        columnRectArray = [self addRect:tempRect rectArray:columnRectArray];
        [columnRectArray sortUsingComparator:^NSComparisonResult(id  _Nonnull obj1, id  _Nonnull obj2) {
            
            return CGRectGetMaxY([obj1 CGRectValue]) > CGRectGetMaxY([obj2 CGRectValue]);
        }];
    }
    return columnRectArray;
}

- (void)updatefooterViewAttributesArray:(NSUInteger)section 
                                    and:(CGFloat *)footerViewHeight
                               contentH:(CGFloat)contentRectHeight
                               contentY:(CGFloat)contentRectY {
    NSIndexPath *sectionIndexPath = [NSIndexPath indexPathForRow:0 inSection:section];
    UIEdgeInsets sectionInset = [self sectionInsetAtSection:section];
    CGSize referenceSize = [self.delegate collectionView:self.collectionView 
                                                  layout:self referenceSizeForFooterInSection:section];
    CGSize footerViewSize = CGSizeMake(fRound(referenceSize.width), fRound(referenceSize.height));
    *footerViewHeight = footerViewSize.height;
    CGRect footerViewFrame = (CGRect){{0, contentRectHeight + contentRectY + sectionInset.bottom}, footerViewSize};
    UICollectionViewLayoutAttributes *footerViewAttributes = 
    [UICollectionViewLayoutAttributes layoutAttributesForSupplementaryViewOfKind:UICollectionElementKindSectionFooter
                                                                   withIndexPath:sectionIndexPath];
    footerViewAttributes.frame = footerViewFrame;
    if ([self.footerViewAttributesArray safeObjectAtIndex:section]) {
        
        [self.footerViewAttributesArray replaceObjectAtIndex:section withObject:footerViewAttributes];
    } else {
        
        [self.footerViewAttributesArray addObject:footerViewAttributes];
    }
}

- (void)updateSectionRectArray:(NSUInteger)section
                           and:(CGRect)sectionRect
                         space:(CGFloat)sectionSpace {
    
    if (sectionSpace == 0.5) {
        
        sectionRect.size.height += 0.5;
        sectionRect.origin.y -= sectionSpace;
    }
    
    if ([self.sectionRectArray safeObjectAtIndex:section]) {
        
        [self.sectionRectArray replaceObjectAtIndex:section 
                                         withObject:[NSValue valueWithCGRect:sectionRect]];
    } else {
        
        [self.sectionRectArray addObject:[NSValue valueWithCGRect:sectionRect]];
    }
}

- (void)updateDecorationViewAttributesArray:(NSUInteger)section and:(CGRect)sectionRect {
    NSIndexPath *sectionIndexPath = [NSIndexPath indexPathForRow:0 inSection:section];
    FNFreshMrFreshDecorationLayoutAttributes *decorationViewAttributes = 
    [FNFreshMrFreshDecorationLayoutAttributes layoutAttributesForDecorationViewOfKind:kMrFreshDecorationView
                                                                        withIndexPath:sectionIndexPath];
    
    decorationViewAttributes.zIndex = -1;
    decorationViewAttributes.frame = sectionRect;
    decorationViewAttributes.backgroundColor = [self backgroundColorAtSection:section];
    decorationViewAttributes.bgImgUrl = [self bgImgUrlAtSection:section];
    if ([self.decorationViewAttributesArray safeObjectAtIndex:section]) {
        
        [self.decorationViewAttributesArray replaceObjectAtIndex:section 
                                                      withObject:decorationViewAttributes];
    } else {
        
        [self.decorationViewAttributesArray addObject:decorationViewAttributes];
    }
}

- (CGRect)rectForSection:(NSInteger)section {
    
    if (section < 0 || section >= self.sectionRectArray.count) {
        
        return CGRectZero;
    }
    return [self.sectionRectArray[section] CGRectValue];
}

- (CGFloat)spaceAtSection:(NSInteger)section {
    
    CGFloat space = 0;
    if ([self.delegate respondsToSelector:@selector(collectionView:layout:spaceAtSection:)]) {
        
        space = [self.delegate collectionView:self.collectionView layout:self spaceAtSection:section];
    }
    return space;
}

- (UIEdgeInsets)sectionInsetAtSection:(NSInteger)section {
    
    UIEdgeInsets sectionInset = self.sectionInset;
    if ([self.delegate respondsToSelector:@selector(collectionView:layout:insetForSectionAtIndex:)]) {
        
        sectionInset = [self.delegate collectionView:self.collectionView layout:self insetForSectionAtIndex:section];
    }
    return sectionInset;
}

- (CGFloat)minimumLineSpacingAtSection:(NSInteger)section {
    
    CGFloat lineSpace = self.minimumLineSpacing;
    if ([self.delegate respondsToSelector:@selector(collectionView:layout:minimumLineSpacingForSectionAtIndex:)]) {
        
        lineSpace = [self.delegate collectionView:self.collectionView 
                                           layout:self
              minimumLineSpacingForSectionAtIndex:section];
    }
    return lineSpace;
}

- (CGFloat)minimumInteritemSpacingAtSection:(NSInteger)section {
    
    CGFloat itemSpace = self.minimumInteritemSpacing;
    if ([self.delegate respondsToSelector:@selector(collectionView:layout:minimumInteritemSpacingForSectionAtIndex:)]) {
        
        itemSpace = [self.delegate collectionView:self.collectionView 
                                           layout:self
         minimumInteritemSpacingForSectionAtIndex:section];
    }
    return itemSpace;
}

- (CGSize)itemSizeAtIndexPath:(NSIndexPath *)indexPath {
    
    CGSize itemSize = CGSizeZero;
    if ([self.delegate respondsToSelector:@selector(collectionView:layout:sizeForItemAtIndexPath:)]) {
        
        itemSize = [self.delegate collectionView:self.collectionView layout:self sizeForItemAtIndexPath:indexPath];
        itemSize = CGSizeMake(fRound(itemSize.width), itemSize.height);
    }
    return itemSize;
}

- (NSUInteger)sectionAtPoint:(CGPoint)point {
    NSInteger y = point.y;
    NSUInteger index = NSNotFound;
    for (NSInteger i = 0; i < self.sectionRectArray.count; i++) {
        CGRect rect = [[self.sectionRectArray safeObjectAtIndex:i] CGRectValue];
        if (y < rect.origin.y + rect.size.height && y > rect.origin.y) {
            index = i;
            break;
        }
    }
    return index;
}

- (NSUInteger)currentSectionAtCollectionViewContentOffset {
    
    CGPoint point = self.collectionView.contentOffset;
    point.y += topBarHeight();
    return [self sectionAtPoint:point];
}

- (BOOL)rectIntersectsRect:(CGRect)rect rectArray:(NSArray *)rectArray {
    
    for (NSValue *value in rectArray) {
        
        CGRect rectLoop = [value CGRectValue];
        if (CGRectIntersectsRect(rect, rectLoop)) {
            
            return YES;
        }
    }
    return NO;
}

- (NSMutableArray *)addRect:(CGRect)rect rectArray:(NSArray *)rectArray {
    
    NSMutableArray *mutableArray = [NSMutableArray array];
    CGRect finialRect = rect;
    for (NSValue *value in rectArray) {
        
        CGRect loopRect = value.CGRectValue;
        if (fequal(CGRectGetMaxY(finialRect), CGRectGetMaxY(loopRect)) && 
            fequal(CGRectGetMinX(finialRect), CGRectGetMaxX(loopRect))) {
            
            CGRect unionRect = CGRectUnion(finialRect, loopRect);
            finialRect = unionRect;
        } else if (CGRectIntersectsRect(finialRect, loopRect)) {
            
            CGRect intersectionRect = CGRectIntersection(finialRect, loopRect);
            CGRect remainder;
            CGRectDivide(loopRect, &intersectionRect, &remainder, intersectionRect.size.width, CGRectMinXEdge);
            [mutableArray addObject:[NSValue valueWithCGRect:remainder]];
        } else {
            
            [mutableArray addObject:value];
        }
    }
    [mutableArray addObject:[NSValue valueWithCGRect:finialRect]];
    return mutableArray;
}

- (UIColor *)backgroundColorAtSection:(NSInteger)section {
    
    UIColor *color = nil;
    if ([self.delegate respondsToSelector:@selector(collectionView:layout:backgroundColorForSection:)]) {
        
        color = [self.delegate collectionView:self.collectionView layout:self backgroundColorForSection:section];
    }
    return color;
}

- (NSString *)bgImgUrlAtSection:(NSInteger)section {
    NSString *bgImgUrl = nil;
    if ([self.delegate respondsToSelector:@selector(collectionView:layout:bgImgUrlForSection:)]) {
        bgImgUrl = [self.delegate collectionView:self.collectionView layout:self bgImgUrlForSection:section];
    }
    return bgImgUrl;
}

- (NSIndexSet *)visiableSectionInRect:(CGRect)rect {
    
    NSMutableIndexSet *indexSet = [NSMutableIndexSet indexSet];
    if (self.sectionRectArray.count == 0) {
        
        return indexSet;
    }
    CGRect theRect = rect;
    for (NSInteger i = 0; i < self.sectionRectArray.count; i ++) {
        
        CGRect sectionRect = [[self.sectionRectArray safeObjectAtIndex:i] CGRectValue];
        
        if (CGRectIntersectsRect(theRect, sectionRect)) {
            
            [indexSet addIndex:i];
        }
    }
    return indexSet;
}

#pragma mark - getters

- (NSMutableArray *)sectionRectArray {
    
    if (!_sectionRectArray) {
        
        _sectionRectArray = [NSMutableArray array];
    }
    return _sectionRectArray;
}

- (NSMutableArray *)headerViewAttributesArray {
    
    if (!_headerViewAttributesArray) {
        
        _headerViewAttributesArray = [NSMutableArray array];
    }
    return _headerViewAttributesArray;
}

- (NSMutableArray *)itemAttributesArray {
    
    if (!_itemAttributesArray) {
        
        _itemAttributesArray = [NSMutableArray array];
    }
    return _itemAttributesArray;
}

- (NSMutableArray *)footerViewAttributesArray {
    
    if (!_footerViewAttributesArray) {
        
        _footerViewAttributesArray = [NSMutableArray array];
    }
    return _footerViewAttributesArray;
}

- (NSMutableArray *)decorationViewAttributesArray {
    
    if (!_decorationViewAttributesArray) {
        
        _decorationViewAttributesArray = [NSMutableArray array];
    }
    return _decorationViewAttributesArray;
}

- (id<FNMrFreshCollectionViewDelegateFlowLayout>)delegate {
    
    return (id<FNMrFreshCollectionViewDelegateFlowLayout>)self.collectionView.delegate;
}


@end
