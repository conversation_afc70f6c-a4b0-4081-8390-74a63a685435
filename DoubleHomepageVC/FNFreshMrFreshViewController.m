//
//  FNFreshMrFreshViewController.m
//  FNFresh
//
//  Created by yong<PERSON><PERSON> on 2017/2/27.
//  Copyright © 2017年 FeiNiu. All rights reserved.
//  牛鲜生

#import "FNFreshMrFreshViewController.h"
#import "FNFreshMrFreshLocationPermissionView.h"
#import "FNFreshMrFreshViewModel.h"
#import "UIViewController+FNMrFreshAnimatedTransitioning.h"
#import "UIViewController+FNFreshEmptyDataSet.h"
#import <FNCacheManager.h>
#import "FNFreshUrlRouter.h"
#import <UIViewController+FNNavigationBarHidden.h>
#import "NSObject+FnFreshEncryptPersistence.h"
#import <FBKVOController.h>
#import "FNFreshMerchandiseAddToCartHandler.h"
#import "FNFreshMrFreshGradientView.h"
#import "FNFreshMrFreshFaultToleranceView.h"
#import "FNFreshMrFreshSendGiftOnLoadResponseModel.h"
#import "FNFreshRecommendTipView.h"
#import "FNMediator+FNFreshH5Module.h"
#import "UIView+AddShopcartAnimation.h"
#import "FNFreshMrFreshEatInSuspensionView.h"
#import "FNFreshMrFreshEatInParameterModel.h"
#import "FNMediator+FNFreshListModule.h"
#import "FNFreshFirstShowGiftParameterModel.h"
#import "FNFreshMrFreshFeedsGoodsViewController.h"
#import "FNFreshMrFreshAcToGoVipProtocolResponse.h"
#import "FNFreshMrFreshProtocolPopupViewController.h"
#import "FNFreshMrFreshConstantCacheHandler.h"
#import "FNSearchCommandCouponResponseModel.h"
#import "FNFreshCouponRedeemedSuccessVC.h"
#import "FNFreshMrFreshViewController+OrderState.h"
#import "FNFreshGPSAddressParameterModel.h"
#import "FNFreshMrFreshStoreListParameter.h"
#import "FNFreshMrFreshPopStoreListViewController.h"
#import "FNFreshCouponRechargeParameterModel.h"
#import "NSString+Validate.h"
#import "FNFreshCouponRechargeResponseModel.h"
#import "FNFreshMyCouponAlertViewController.h"
#import "FNFreshMrFreshViewController+popupWindow.h"
#import "FNFreshMrFreshViewController+RegisterCollectionCell.h"
#import "FNFreshMrFreshGetArriveTimeResponse.h"
#import "FNFreshTabBarController.h"
#import "FNHomeSearchTableView.h"

#import "FNFreshMrFreshService.h"
#import "FNFreshMrFreshParameterModel.h"
#import "FNFreshMrFreshPopupWindowParameterModel.h"
#import "FNFreshLocationLogic.h"
#import "FNFreshCardService.h"
#import "FNFreshMessageResponseModel.h"
#import "FNFreshMrFreshHomeScanParameter.h"
#import "FNFreshMrFreshHomeScanResponse.h"
#import "FNFreshMrFreshLocationBottomView.h"
#import "FNFreshMrFreshHangAdsView.h"
#import "FNSCanViewController.h"
#import "FNFreshUserCenterSettingAboutViewController.h"

#import "FNFreshMrFreshAnnouncementChildTableViewCell.h"
#import "FNHomeVerticalScrollTextView.h"
#import "FNFreshMrFreshCouponWillExpireTip.h"
#import "FNFreshCouponViewController.h"
//Mediator
#import "FNMediator+FreshSearchModule.h"
#import "FNMediator+FreshAddressModule.h"
#import "FNMediator+FreshCouponModel.h"
#import "FNMediator+FNFreshMessageModule.h"
#import "FNMediator+FNFreshDepartmentModule.h"
#import "FNMediator+FNFreshShoppingCardModule.h"
#import "FNFreshSearchParameterModel.h"
#import "FNFreshSearchResponseModel.h"
#import "FNFreshSearchService.h"

#import "FNFreshNewACToGoHomeRefreshHeader.h"
#import "FNFreshNewHomeRefreshHeader.h"
#import "FNFreshMrFreshTooltipView.h"
#import "FNFreshMrFreshSwitchLocationaddrView.h"
#ifdef ISFRAMEWORK
#else
#import "FNFreshADPageViewController.h"
#endif

#import "FNFreshStoreServiceNotification.h"
#import "FNFreshMrFreshHomeKeywordsParameter.h"
#import "FNFreshMrFreshHomeKeywordsResponse.h"
#import "FNMediator+FNSQShopModule.h"

#import <FNLog/FNLogManager.h>
#import <WebKit/WebKit.h>
#import "FNH5WeakProxy.h"

#import "IQKeyboardManager.h"
#import "FNFreshGrowthHelper.h"
#import "FNFreshEsShopCartHelper.h"
#import "FNSQShopManager.h"
#import "NSString+FNCBCAEScryption.h"


#import "FNMediator+FNFreshShopCartModule.h"

static inline CGFloat navigationBarHeight(void) {
    return 44.0;
}

typedef NS_ENUM(NSInteger, FNMrFreshNavigationBarStyle) {
    FNMrFreshNavigationBarStyleNormal,
    FNMrFreshNavigationBarStyleAbnormal,
};

NSNotificationName const kFNFreshRefreshHomeDataNotification = @"FNRefreshHomeDataNotification";
NSNotificationName const kFNFreshScrollToTopNotification = @"FNFreshScrollToTopNotification";

NSString *const UICollectionReusableViewIdentifier = @"UICollectionReusableView";

NSString *const FNFreshMrFreshMoreHeaderCollectionReusableViewIdentifier =
@"FNFreshMrFreshMoreHeaderCollectionReusableView";

NSString *const FNFreshMrFreshPrintingHotStyleHeaderCollectionReusableViewIdentifier =
@"FNFreshMrFreshPrintingHotStyleHeaderCollectionReusableView";

NSString *const FNFreshMrFreshFooterCollectionReusableViewIdentifier =
@"FNFreshMrFreshFooterCollectionReusableView";

NSString *const FNFreshMrFreshCommonACollectionReusableViewIdentifier =
@"FNFreshMrFreshCommonACollectionReusableView";

NSString *const FNFreshMrFreshShufflingBannerCollectionViewCellIdentifier =
@"FNFreshMrFreshShufflingBannerCollectionViewCell";

NSString *const FNFreshMrFreshAnnouncementCollectionViewCellIdentifier =
@"FNFreshMrFreshAnnouncementCollectionViewCell";

NSString *const FNFreshMrFreshManagerRecommendCollectionViewCellIdentifier =
@"FNFreshMrFreshManagerRecommendCollectionViewCell";

static NSString *const FNFreshMrFreshAnnouncementChildTableViewCellIdentifier =
@"FNFreshMrFreshAnnouncementChildTableViewCell";

static NSString *const FNMrFreshNoNetwork = @"网络不给力，请重试";
static NSString *const FNMrFreshLocationFailure = @"无法获取定位，请手动切换";

NSString *const FNMrFreshLastHomePopupWindowTime = @"FNMrFreshLastHomePopupWindowTime";
NSString *const FNMrFreshLastHomePopupWindowSchedule = @"FNMrFreshLastHomePopupWindowSchedule";
NSString *const FNMrFreshLastHomePopupIndex = @"FNMrFreshLastHomePopupIndex";
NSString *const FNMrFreshLastBirthdayBombTime = @"FNMrFreshLastBirthdayBombTime";
NSString *const FNMrFreshLastNewGuidancePopupWindowTime = @"FNMrFreshLastNewGuidancePopupWindowTime";
NSString *const FNMrFreshLastCommonPopupWindowTime = @"FNMrFreshLastCommonPopupWindowTime";
NSString *const FNMrFreshLastCommonPopupWindowSchedule = @"FNMrFreshLastCommonPopupWindowSchedule";
NSString *const FNMrFreshLastAppOpenTime = @"FNMrFreshLastAppOpenTime";
NSString *const FNMrFreshOpenGiftPopupTime = @"FNMrFreshOpenGiftPopupTime";
NSString *const FNMrFreshCRMCouponPopupTime = @"FNMrFreshCRMCouponPopupTime";
NSString *const FNMrFreshCouponBombTime = @"FNMrFreshCouponBombTime";
NSString *const FNMrFreshSaveMoneyCardTime = @"saveMoneyCardTime";
NSString *const FNMrFreshOldNForOneTime = @"oldNForOneTime";
NSString *const FNMrFreshAtmosphereH5ShowTime = @"atmosphereH5ShowTime";

/**
 * 第一次安装app展示定位权限页面
 */
NSString *const FNMrFreshOpenLocationPermissionShowed = @"FNMrFreshOpenLocationPermissionShowed";

static NSString *const FNMrFreshRecommendTipShowed = @"FNMrFreshRecommendTipShowed";
static NSString *const FNMrFreshSaleGuideTipShowed = @"FNMrFreshSaleGuideTipShowed";
NSString *const FNMrFreshACToGoAnnouncement = @"FNMrFreshACToGoAnnouncement"; // 合并欧尚公告弹框 整个app周期只弹一次
NSString *const FNMrFreshACToGoAnnouncementForDay = @"FNMrFreshACToGoAnnouncementForDay"; // 每天弹一次
NSString *const FNMrFreshACToGoAnnouncementNoRemind = @"FNMrFreshACToGoAnnouncementNoRemind"; // 不再提醒
NSString *const FNMrFreshTaskBottomTipShowed = @"FNMrFreshTaskBottomTipShowed";
static NSString *const FNMrFreshSwitchStoreListTipForDay = @"FNMrFreshSwitchStoreListTipForDay";
//第一次安装app，定位权限弹框---默认背景容错页
static NSString *const FNMrFreshFirstEnterAppWithFaultView = @"FNMrFreshFirstEnterAppWithFaultView";
static NSString *const FNMrFreshFirstEnterAppWithFaultViewFromNot = @"FNMrFreshFirstEnterAppWithFaultViewFromNot";
//push权限弹框，优先级最低，每次安装app后检查权限，仅弹一次
NSString *const FNMrFreshFirstEnterAppPushNot = @"FNMrFreshFirstEnterAppPushNot";

static const CGFloat FNMrFreshTopNormalNavigationBarHeight = 83;

static BOOL riseIsChange = NO;
static CGFloat compareRise = -1;

@interface FNFreshMrFreshViewController () <FNFreshMerchandiseAddToCartHandlerDelegate, 
FNFreshMrFreshFeedsGoodsGestureDelegate,
UITableViewDelegate, UITableViewDataSource,
WKScriptMessageHandler>

@property (weak, nonatomic) IBOutlet FNFreshMrFreshGradientView *restView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *restViewHeightConstraint;
@property (weak, nonatomic) IBOutlet UILabel *restLabel;
@property (weak, nonatomic) IBOutlet UILabel *restNameLabel;
@property (weak, nonatomic) IBOutlet UIImageView *qualificationView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *qualificationViewBottomConstraint;

@property (weak, nonatomic) IBOutlet NSLayoutConstraint *collectionViewTopConstraint;
@property (weak, nonatomic) IBOutlet UIView *normalNavigationBar;
@property (weak, nonatomic) IBOutlet UIView *abnormalNavigationBar;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *configBgViewTopConstraint;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *configBgViewHeightConstriant;
@property (weak, nonatomic) IBOutlet UIImageView *bgImgView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *bgImgViewTopConstraint;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *bgImgViewHeightConstraint;

@property (weak, nonatomic) IBOutlet FNFreshMrFreshGradientView *searchBackgroundView;
@property (weak, nonatomic) IBOutlet UIView *searchView;
@property (weak, nonatomic) IBOutlet UIView *locationView;
@property (weak, nonatomic) IBOutlet UILabel *addressLab;
@property (weak, nonatomic) IBOutlet FNHomeVerticalScrollTextView *storeNameDynamicScrollView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *storeNameScrollViewWidthConstraint;

@property (weak, nonatomic) IBOutlet UILabel *keywordLabel;
@property (weak, nonatomic) IBOutlet UILabel *emptyAccessoryLabel;
@property (weak, nonatomic) IBOutlet UILabel *messageCountLab;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *messageCountWidthConstraint;
@property (weak, nonatomic) IBOutlet UIView *changeStoreView;
@property (weak, nonatomic) IBOutlet UIView *messageView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *abnormalNavigationViewHeightConstraint;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *normalNavigationViewHeightConstraint;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *restContentViewTopConstraint;

@property (assign, nonatomic, getter=isEmpty) BOOL empty;
@property (assign, nonatomic, getter=isRefreshing) BOOL refreshing;
@property (assign, nonatomic, getter=isBlackStateBar) BOOL blackStateBar;                //状态栏颜色
@property (assign, nonatomic, getter=isFirstEntry) BOOL firstEntry;                      //第一次进入正常首页

@property (strong, nonatomic) FNFreshRecommendTipView *recommendTipView;

@property (strong, nonatomic) dispatch_group_t requestGroup;

@property (strong, nonatomic) FNFreshMerchandiseAddToCartHandler *addToCartHandler;

@property (strong, nonatomic) UIImageView *freshSaleGuideTip;     // V116新增鲜特卖更多引导图
@property (assign, nonatomic, getter=canReportGuessMTAgent) BOOL reportGuessMTAgent; // 是否上报猜你喜欢埋点
@property (assign, nonatomic, getter=canReportHotZoneMTAgent) BOOL reportHotZoneMTAgent; // 是否上报爆款专区埋点

@property (assign, nonatomic) BOOL lastRepot;              // 记录“猜你喜欢”上一次上报
@property (assign, nonatomic) BOOL hotZoneLastRepot;              // 记录“爆款专区”上一次上报

@property (assign, nonatomic) NSInteger errorType; // 请求的首页类型，0：正常首页，1：容错页，2：游客容错页。不传的时候默认返回正常首页
@property (assign, nonatomic, getter=isLeaveADPageVC) BOOL leaveADPageVC; // 离开欢迎页进入首页
@property (assign, nonatomic, getter=isShowMsgCenter) BOOL showMsgCenter; // 消息中心入口显隐藏

// 加入购物车动画--- 记录添加的商品图片 、frame
@property (strong, nonatomic) UIImage *addToCardImgView;
@property (assign, nonatomic) CGRect addToCardImgRect;

// 堂食悬浮view
@property (strong, nonatomic) FNFreshMrFreshEatInSuspensionView *eatInSuspensionView;
@property (strong, nonatomic) FNFreshMrFreshHangAdsView *hangAdImgView; // 悬浮广告
// 首页分屏加载
@property (assign, nonatomic) BOOL hasLoadSecondScreen; // 是否已加载第二屏
/// Feeds流是否能滑动
@property (assign, nonatomic) BOOL cannotScroll;
@property (assign, nonatomic) BOOL isRocketShow; // 小火箭
/// Feeds流上部内容是否超过一屏
@property (assign, nonatomic) BOOL contentIsMoreThanScreen;

// feeds流
@property (nonatomic, assign) CGFloat feedsCriticalPointOffsetY;
@property (nonatomic, assign) CGPoint lastEndDragVelocity;
@property (nonatomic) CGPoint targetContentOffset;

@property (nonatomic, strong) UIScrollView *emptyScrollView;
@property (weak, nonatomic) IBOutlet UILabel *emptyNavTitleLab;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *emptyNavBgViewHeightConstraint;
@property (strong, nonatomic) FNFreshMrFreshStoreListResponse *storeListData;
@property (weak, nonatomic) FNSCanViewController *scanVC;
@property (weak, nonatomic) IBOutlet FNHomeSearchTableView *keywordsTabelView;
@property (strong, nonatomic) NSTimer *keywordsTimer;
@property (strong, nonatomic) CIFilter *grayFilter;
@property (assign, nonatomic) BOOL isLastGrayFliter;

// 底部地址提示栏
@property (strong, nonatomic) FNFreshMrFreshLocationBottomView *bottomAddressView;
// 定位权限浮层view
@property (strong, nonatomic) FNFreshMrFreshLocationPermissionView *locationPermissionView;
// 优惠券过期提示view
@property (strong, nonatomic) FNFreshMrFreshCouponWillExpireTip *couponExpireTipView;
// 首页keywords 数据源
@property (copy, nonatomic) NSArray<FNMrFreshKeywordsItemModel *> *keywords;

@property (assign, nonatomic) NSTimeInterval startRequestHomeTimeInterval;

@property (assign, nonatomic) BOOL isScrollToFeeds;

@property (weak, nonatomic) WKWebView *homeWebView;
@property (strong, nonatomic) FNH5WeakProxy *weakProxy;
@property (strong, nonatomic) NSTimer *webViewAutoDismissTimer;

/// APP 是否第一次启动
@property (nonatomic, assign) BOOL isFirstLaunched;

@end

@implementation FNFreshMrFreshViewController

#pragma mark - tabel view delegate dataSource

/// 启动时间 - 性能优化（启动时运行一次）
- (void)applicationDidFinishLaunching {
    if (!self.isFirstLaunched) {
        return;
    }
    
    /// 键盘输入设置
    [[IQKeyboardManager sharedManager] setEnable:NO];
    [[IQKeyboardManager sharedManager] setEnableAutoToolbar:NO];
    
    /// 15s计时 积分成长值获取
    [[FNFreshGrowthHelper shareInstance] growthStartTimer];
    [[FNFreshEsShopCartHelper sharedInstance] requestBiometryStatus];
    
    /// 获取所有商品的加购信息，为了同步商品列表的商品加购数量
    [[FNFreshUtils shareInstance] loadAllMerchantListCountData];
    
    /// 获取 tabBar 购物车 icon 上的角标数量
    if ([FNFreshUser shareInstance].shopId.length > 0) {
        [[FNMediator sharedInstance] fnFreshShopCartService_RequestGetShopCartMerchandiseTotalNumber];
    }
    
    /// 请求全球购对应的用户信息
    [self getSQShopUserInfo];
    
    self.isFirstLaunched = NO;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.keywords.count > 1 ? 3 : 1;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return section == 1 ? self.keywords.count : 1;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FNFreshMrFreshAnnouncementChildTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:FNFreshMrFreshAnnouncementChildTableViewCellIdentifier];
    NSInteger index = [self indexWithIndexPath:indexPath];
    FNMrFreshKeywordsItemModel *item = [self.keywords safeObjectAtIndex:index];
    if (item) {
        cell.keywords = item;
    } else {
        FNMrFreshKeywordsItemModel *defaultItem = [[FNMrFreshKeywordsItemModel alloc] init];
        defaultItem.words = @"寻找称心商品";
        cell.keywords = defaultItem;
    }
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];

    FNHomeSearchTableView *customTableView = (FNHomeSearchTableView *)tableView;
    NSLog(@"custom table did select at point is %@",NSStringFromCGPoint(customTableView.lastTouchPoint));
    CGRect searchBtnRect = [self.searchView convertRect:self.searchBtn.frame toView:self.view.window];
    NSLog(@"search btn's frame at window is %@", NSStringFromCGRect(searchBtnRect));
    BOOL isContainer = CGRectContainsPoint(searchBtnRect, customTableView.lastTouchPoint);

    NSInteger index = [self indexWithIndexPath:indexPath];
    FNMrFreshKeywordsItemModel *item = [self.keywords safeObjectAtIndex:index];
    // 点击了搜索按钮
    if (isContainer && item) {
        if ([item.words isEqualToString:@"寻找称心商品"]) {
            [self gotoSearchWithKeywords:item.words isResult:NO];
            return;
        }
        /**
         * 判断是否点击在搜索按钮上
         * 首页点击搜索按钮or在搜索准备页点击搜索按钮时，词的跳转（有可能是框内词也有可能是搜索发现词）：
         * 1、先看是否=配了跳转链接的框内词，如果配了则跳转对应页面，
         * 2、如果1没配则再看是否=配了链接的搜索发现词，如果=配了链接的搜索发现词则跳对应页面，
         * 3、如果2没配再看是否=活动页搜词，如果=活动页搜词则跳活动页
         * 4、如果3不=，则跳搜索结果页
         */

        NSString *jmpUrl = [self.viewModel.hotJmpDic safeObjectForKey:item.words];

        if (jmpUrl.length > 0) {
            [self openPageWithURLString:jmpUrl];
        } else {
            // 请求是否是活动页搜词
            // 触发接口查询
            FNFreshSearchParameterModel *parameterModel = [[FNFreshSearchParameterModel alloc] init];
            parameterModel.storeID = [FNFreshUser shareInstance].shopId;
            parameterModel.keywords = item.words;
            __weak typeof(self)weakSelf = self;
            [FNFreshSearchService requstCMSActivityWithParameter:parameterModel
                                                         success:^(FNFreshSearchResponseModel* responseObject, BOOL isCache) {
                if (responseObject.cmsActivityLinkUrl.length > 0) {
                    [weakSelf openPageWithURLString:responseObject.cmsActivityLinkUrl];
                } else {
                    [weakSelf gotoSearchWithKeywords:item.words isResult:YES];
                }
            } failure:^(id responseObject, NSError *error) {
                [weakSelf gotoSearchWithKeywords:item.words isResult:YES];
            }];
        }
        
        [FNFreshAgent eventWithTrackDataPrameters: @{
            @"page_col":@"197021",
            @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
            @"track_type":@"2",
        }];
        return;
    }
    // 点击cell
    [self gotoSearchWithKeywords:item.words isResult:NO];
}

/// 跳转搜索页
/// - Parameters:
///   - keywords: 关键词
///   - isResult: 是否直接跳转搜素结果页
- (void)gotoSearchWithKeywords:(NSString *)keywords isResult:(BOOL)isResult {

    if (isResult) {
        UIViewController *viewController =
        [[FNMediator sharedInstance] freshListModule_searchProductListViewControllerWithKeyWord:keywords
                                                                                       category:@""
                                                                                     completion:nil];
        [FNFreshTabBarController pushViewController:viewController animated:NO];
        [self.viewModel handleCacheRecentSearchKeywordWith:keywords];
        return;
    }
    UIViewController *viewController = [[FNMediator sharedInstance] freshSearchModule_FNFreshSearchViewController_InitWithKeyword:keywords ?: @"寻找称心商品"];
    [[FNFreshTabBarController shareInstance] pushViewController:viewController animated:YES];
    [FNFreshAgent eventWithTrackDataPrameters: @{
        @"page_col":@"100057",
        @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
        @"track_type":@"2",
    }];
}

#pragma mark - life cycle

- (void)viewDidLoad {
    [super viewDidLoad];

    /// 是第一次启动
    self.isFirstLaunched = YES;
    
    // 第一次安装app，定位权限弹框---默认背景容错页
    // v178 更新为：定位弹框如果没有选择的话，默认展示不在配送中的容错页
    if ([CLLocationManager authorizationStatus] == kCLAuthorizationStatusNotDetermined) {
        [self setOutOfScopeErrorWithAddress:nil isMini:YES];
    }

    [self setup];
    [self registerCollectionReusableView];
    [self registerTableViewReusableCell];
    [self addObserver];
    self.requestGroup = dispatch_group_create();
    self.abnormalNavigationViewHeightConstraint.constant = topBarHeight();
    self.emptyNavBgViewHeightConstraint.constant = topBarHeight();
    self.normalNavigationViewHeightConstraint.constant = FNMrFreshTopNormalNavigationBarHeight + (kStatusBarHeight);
    [self checkADPageViewController];
    [self locationInformation];

    if (!isFNFreshTarget && [FNFreshUser shareInstance].isLogin) {
        [self requestAcToGoVipProtocol];
    }

    // 如果全局定位接口展示mini门店或者新店，此时进入app门店首页，当点击首页tab，进入didload方法，
    // 此时判断[FNFreshUser shareInstance].enterHomeType，展示对应的容错页
    if ([FNFreshUser shareInstance].enterHomeType == FNFreshMiniStoreType) {
        [self setOutOfScopeErrorWithAddress:nil isMini:YES];
    } else if ([FNFreshUser shareInstance].enterHomeType == FNFreshNewOfflineType) {
        [self setOutOfScopeErrorWithAddress:nil isMini:NO];
    }
}

- (BOOL)locationPermissionViewHidden {
    BOOL hidden = NO;
    CLAuthorizationStatus status;
    if (@available(iOS 14.0, *)) {
        status = [[[CLLocationManager alloc] init] authorizationStatus];
    } else {
        status = [CLLocationManager authorizationStatus];
    }
    if (status == kCLAuthorizationStatusAuthorizedAlways || status == kCLAuthorizationStatusAuthorizedWhenInUse || status == kCLAuthorizationStatusDenied) {
        hidden = YES;
    }
#ifdef APPSTORERELEASE

#else
    NSString *stat = @"CLAuthorizationStatus";
    switch (status) {
        case kCLAuthorizationStatusAuthorizedAlways:
            stat = @"kCLAuthorizationStatusAuthorizedAlways";
            break;
        case kCLAuthorizationStatusAuthorizedWhenInUse:
            stat = @"kCLAuthorizationStatusAuthorizedWhenInUse";
            break;
        case kCLAuthorizationStatusDenied:
            stat = @"kCLAuthorizationStatusDenied";
            break;
        case kCLAuthorizationStatusNotDetermined:
            stat = @"kCLAuthorizationStatusNotDetermined";
            break;
        case kCLAuthorizationStatusRestricted:
            stat = @"kCLAuthorizationStatusRestricted";
            break;
        default:
            stat = @"CLAuthorizationStatusDefault";
            break;
    }
    DFileLog(@"首页定位浮层日志：status = %@, LocationPermissionShowed = %@", stat, (NSNumber *)[self readFromFile:FNMrFreshOpenLocationPermissionShowed]);
#endif
    return hidden;
//    return ([(NSNumber *)[self readFromFile:FNMrFreshOpenLocationPermissionShowed] boolValue] && hidden);
}

- (void)viewWillAppear:(BOOL)animated {

    [super viewWillAppear:animated];
    self.currentPage = YES;
    if (!isFNFreshTarget && [FNFreshUser shareInstance].isLogin && !self.isFirstEntry) {
        [self requestAcToGoVipProtocol];
    }

    if (self.isLastGrayFliter) {
        [[UIApplication sharedApplication] delegate].window.layer.filters = @[self.grayFilter];
    }
    self.locationPermissionView.hidden = [self locationPermissionViewHidden];
    [[FNFreshTabBarController shareInstance] hideTabbar:![self locationPermissionViewHidden]];
    
}

- (void)viewDidAppear:(BOOL)animated {

    [super viewDidAppear:animated];

    // 确保tab切换状态完全稳定后再执行相关操作
    __weak typeof(self) weakSelf = self;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.05 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        // 在这里可以添加需要确保状态稳定后执行的代码
        if (weakSelf && weakSelf.currentPage) {
            // 状态已稳定，可以正常处理用户交互
        }
    });

    /// APP 第一次启动（从 AppDelegate 中移过来的方法）
    [self applicationDidFinishLaunching];

    [self changeTimerState:YES];

    //启动后第一次进首页刷新弹窗接口是需要在有首页数据的前提下 所以这里的刷新主要是用在后面的二次切回首页的操作 并且容错页不做二次刷新
    if (self.isHandlePopwindow && !self.faultToleranceView) {

        [self requestPopupWindowInfo];
    }
    [self requestMemCardTip];
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"100052",
        @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
        @"track_type":@"1",
    }];
    // 新人N选1
    FNFreshMrFreshNewChoiceCollectionViewCell *cell = (FNFreshMrFreshNewChoiceCollectionViewCell *)[self.collectionView cellForItemAtIndexPath:self.nxyIndexPath];
    if (cell) {
        [self.collectionView reloadItemsAtIndexPaths:@[self.nxyIndexPath]];
    }
    
    //老人N选1
    [self reloadOldNXOneCell];
    
    if (self.isCurrentPage && self.faultToleranceView) {
        [[FNFreshTabBarController shareInstance] hideTabbar:YES];
    }
    if (self.isRocketShow && !isFNFreshTarget) {
        self.tabBarItem.title = @"TOP";
    }

    if ([FNFreshUser shareInstance].halfHourIntervalBecomeActive) {
        [FNFreshUser shareInstance].halfHourIntervalBecomeActive = NO;
        [[FNFreshLocationLogic shareInstance] openPositionWithUrl:nil payloadUrl:nil];
    }

    // 搜索框曝光
    [FNFreshAgent eventWithTrackDataPrameters: @{
        @"page_col":@"153002",
        @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
        @"track_type":@"6",
    }];

    if (_homeWebView) {
        [_homeWebView setHidden:NO];
    }
}

- (void)viewWillDisappear:(BOOL)animated {

    [super viewWillDisappear:animated];
    self.currentPage = NO;
    if (self.isShowSaleGuideTip) {
        [self saleGuideTipDisappear];
    }
    [FNFreshUser shareInstance].addrWarn = nil;
    if (_homeWebView) {
        [_homeWebView setHidden:YES];
    }
}

- (void)viewDidDisappear:(BOOL)animated {

    [super viewDidDisappear:animated];
    [self changeTimerState:NO];
    if (self.viewModel.responseModel) {
        //存在responseModel说明已经进入正常首页
        self.firstEntry = NO;
        self.firstEntryShop = NO;
    }
    [[UIApplication sharedApplication] delegate].window.layer.filters = nil;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)registerTableViewReusableCell {
    [self.keywordsTabelView registerNib:[[FNFreshMrFreshAnnouncementChildTableViewCell class] fnFreshNib] forCellReuseIdentifier:FNFreshMrFreshAnnouncementChildTableViewCellIdentifier];
}

- (void)reloadOldNXOneCell {
    //老人N选1
    FNFreshMrFreshRegularCustomerNXOneCell *oldNXOneCell = (FNFreshMrFreshRegularCustomerNXOneCell *)[self.collectionView cellForItemAtIndexPath:self.oldNXOneIndexPath];
    if (oldNXOneCell) {
        [self.collectionView reloadItemsAtIndexPaths:@[self.oldNXOneIndexPath]];
    }
}

/// 请求全球购对应的用户信息
- (void)getSQShopUserInfo {
    if ([FNFreshUser shareInstance].isLogin && [FNSQShopManager shared].userInfo == nil) {
        [[FNMediator sharedInstance] getSQShopUserInfo:nil];
    }
}

#pragma mark - scroll view delegate
- (void)scrollViewWillEndDragging:(UIScrollView *)scrollView withVelocity:(CGPoint)velocity targetContentOffset:(inout CGPoint *)targetContentOffset {
    self.lastEndDragVelocity = velocity;
    self.targetContentOffset = *targetContentOffset;

    // 滑动到爆款专区模块，曝光上报埋点数据
    [self reportHotStyleZoneMTAgentWithScrollView:scrollView];

    // 鲜特卖Tip
    if ([[FNCacheManager shareMananger] firstOpenForPage:FNMrFreshSaleGuideTipShowed]) {
        return;
    }
    if (self.saleIndexPath) {
        [self showSaleNewGuideView:scrollView.contentOffset.y];
    }
}

- (void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate {
    BOOL isContainable = self.feedsCriticalPointOffsetY - scrollView.contentOffset.y > 10 && self.feedsCriticalPointOffsetY - scrollView.contentOffset.y < 600;

    if (decelerate && self.lastEndDragVelocity.y > 0 && isContainable) {

        CGFloat expectOffsetY = self.lastEndDragVelocity.y * self.lastEndDragVelocity.y / 2 * 1000;
        CGFloat passOffset = scrollView.contentOffset.y + expectOffsetY - self.feedsCriticalPointOffsetY;

        FNFreshMrFreshFeedsCollectionViewCell *cell = (FNFreshMrFreshFeedsCollectionViewCell *)[self.collectionView cellForItemAtIndexPath:self.feedsIndexPath];

        if (cell && [cell isKindOfClass:[FNFreshMrFreshFeedsCollectionViewCell class]] && passOffset > 0) {
            FNFreshMrFreshFeedsGoodsViewController *currentVC = (FNFreshMrFreshFeedsGoodsViewController *)cell.pageViewController.viewControllers.firstObject;
            //            [currentVC makePageViewControllerScroll:YES];
            [currentVC makeScrollTargetOffset:CGPointMake(0, MIN(passOffset,500))];
        }
    }

    // v135版本增加悬浮广告滑动动画
    if (!self.hangAdImgView.isHidden && !decelerate && !self.cannotScroll) {
        [self.hangAdImgView showAdsImg:NO];
    }
}

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    if (!self.hangAdImgView.isHidden && !self.cannotScroll) {
        [self.hangAdImgView showAdsImg:NO];
    }
}

/**
 爆款专区模块曝光埋点上报
 */
- (void)reportHotStyleZoneMTAgentWithScrollView:(UIScrollView *)scrollView {
    if (self.hotStyleZoneIndex) {
        NSIndexPath *hotZoneIndexPath = [NSIndexPath indexPathForRow:0 inSection:self.hotStyleZoneIndex];
        FNFreshMrFreshHotStyleHorizontalCollectionViewCell *cell = (FNFreshMrFreshHotStyleHorizontalCollectionViewCell *)[self.collectionView cellForItemAtIndexPath:hotZoneIndexPath];
        if (!cell) {
            self.hotZoneLastRepot = NO;
            return;
        }
        CGRect rectInCollectView = [self.collectionView convertRect:cell.frame toView:self.collectionView];
        if (scrollView.contentOffset.y > rectInCollectView.origin.y - self.collectionView.bounds.size.height) {
            self.reportHotZoneMTAgent = YES;
        } else {
            self.reportHotZoneMTAgent = NO;
            self.hotZoneLastRepot = NO;
        }
        if (rectInCollectView.origin.y == 0) {
            self.reportHotZoneMTAgent = NO;
            self.hotZoneLastRepot = NO;
        }
        if (self.canReportHotZoneMTAgent && !self.hotZoneLastRepot) {
            [FNFreshAgent eventWithTrackDataPrameters:@{
                @"page_col":@"118005",
                @"page_id" :[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                @"track_type":@"6",
            }];
            self.hotZoneLastRepot = YES;
        }
    }
}

/**
 鲜特卖模块的新手指引
 @param scrollViewOffSetY scrollView.contentOffset.y
 */
- (void)showSaleNewGuideView:(CGFloat)scrollViewOffSetY {
    FNFreshMrFreshSaleCollectionViewCell *cell = (FNFreshMrFreshSaleCollectionViewCell *)[self.collectionView cellForItemAtIndexPath:self.saleIndexPath];
    if (!cell) {
        return;
    }
    CGRect rectInCollectView = [self.collectionView convertRect:cell.frame toView:self.collectionView];
    CGRect rectInSuperView = [self.collectionView convertRect:rectInCollectView toView:self.view];
    NSLog(@"real = %@, super = %@",
          NSStringFromCGRect(rectInCollectView),NSStringFromCGRect(rectInSuperView));
    CGFloat width = SCREEN_WIDTH - 18;
    CGFloat height = floorf((width - 24) / 3 + 69) + 35.f;
    if (scrollViewOffSetY >
        (rectInCollectView.origin.y + height - self.collectionView.bounds.size.height)) {
        __weak typeof(self)weakSelf = self;
        [self saleGuideTipAppearCompleted:^{
            [weakSelf delayPerformSelector];
        }];
        self.showSaleGuideTip = YES;
        [self.freshSaleGuideTip mas_updateConstraints:^(MASConstraintMaker *make) {
            make.bottom.equalTo(self.view.mas_top).mas_offset(floorf(rectInSuperView.origin.y + 10));
        }];
        [[FNCacheManager shareMananger] setFirstOpenedForPage:FNMrFreshSaleGuideTipShowed];
    }
}

- (void)updateBgViewLocationWithContentOffset:(CGPoint)contentOffset {
    if (contentOffset.y > 0) {
        CGFloat configBgViewHeight = self.bannerBgImgView.frame.size.height;
        if (contentOffset.y <= configBgViewHeight) {
            self.configBgViewTopConstraint.constant = -contentOffset.y;
        } else {
            self.configBgViewTopConstraint.constant = -configBgViewHeight;
        }
        if (self.viewModel.responseModel.bgImgUrl) {
            CGFloat bgImgViewHeight = CGRectGetHeight(self.bgImgView.bounds);
            if (contentOffset.y <= bgImgViewHeight) {
                self.bgImgViewTopConstraint.constant = -contentOffset.y;
            } else {
                self.bgImgViewTopConstraint.constant = -CGRectGetHeight(self.bgImgView.bounds);
            }
        }
    } else {
        self.configBgViewTopConstraint.constant = 0;
        self.bgImgViewTopConstraint.constant = 0;
    }
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    /// 判断 内容是否支持滑动，若不支持，则Return
    if ([scrollView isKindOfClass:[UITableView class]]) {
        if (scrollView.contentSize.height <= scrollView.bounds.size.height) {
            return;
        }
        return;
    }
    /// 背景视图随滑动需同步移动
    [self updateBgViewLocationWithContentOffset:scrollView.contentOffset];
    /// 是否数据在刷新中，若在刷新中则Return
    if (self.isRefreshing) {
        return;
    }
    /// 同步导航栏状态
    /// 若隐藏状态，在向上滑动，则进行显示
    CGFloat YOffect = scrollView.contentOffset.y;
    if (self.normalNavigationBar.hidden && YOffect >= 0) {
        self.normalNavigationBar.hidden = NO;
        self.normalNavigationBar.alpha = 1;
    }
    /// 一屏的高度
    CGFloat height = [FNFreshMrFreshConstantCacheHandler shareInstance].oneScreenHeight;
    /// 若向上滑动的距离大于一屏的高度，且TabBar首个items还未显示小火箭，且还在当前页面
    /// 则进行小火箭相关显示
    if (YOffect > height && ![[FNFreshTabBarController shareInstance] isShowRocket] && self.isCurrentPage) {
        self.contentIsMoreThanScreen = YES;
        if (isFNFreshTarget) {
            [[FNFreshTabBarController shareInstance] showRocketAnimated];
        } else {
            self.tabBarItem.selectedImage = [FNFreshMrFreshConstantCacheHandler shareInstance].homeRocketIcon;
            self.tabBarItem.title = @"TOP";
        }
        self.isRocketShow = YES;
        [FNFreshAgent eventWithTrackDataPrameters:@{
            @"page_col":@"127089",
            @"page_id" :[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
            @"track_type":@"6",
        }];
    }
    /// 若向下滑动的距离小于一屏的高度，且TabBar首个items已经显示小火箭，且还在当前页面
    /// 则进行小火箭相关隐藏
    if (YOffect < height && [[FNFreshTabBarController shareInstance] isShowRocket] && self.isCurrentPage) {
        if (isFNFreshTarget) {
            [[FNFreshTabBarController shareInstance] showLogoElephantAnimated];
        } else {
            self.tabBarItem.selectedImage = [FNFreshMrFreshConstantCacheHandler shareInstance].homeIcon;
            self.tabBarItem.title = [FNFreshMrFreshConstantCacheHandler shareInstance].homeStoreTitle;
        }
        self.isRocketShow = NO;
    }
    /// 设置顶部导航栏随滑动同步UI渐变
    [self setNavigationBarUIAnimate:YOffect];
    /// 先特卖的引导如果存在，则进行消失
    if (self.isShowSaleGuideTip) {
        [self saleGuideTipDisappear];
    }
    /// 滑动同步堂食悬浮动画效果
    if (self.viewModel.eatInResponseModel.showEntrance) {
        if (YOffect < 1) {
            [self.eatInSuspensionView openingAnimating];
        } else {
            [self.eatInSuspensionView pickUpAnimating];
        }
    }
    /// 滑动同步悬浮广告动画效果
    if (!self.hangAdImgView.isHidden) {
        CGFloat diff = 1;
        if (YOffect < 1) {
            [self.hangAdImgView showAdsImg:YES];
        } else if (YOffect >= diff) {
            [self.hangAdImgView HiddenAdsImg];
        }
    }
    /// feeds流联动
    [self feedsFlowScrolled:scrollView andOffset:YOffect];
}

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {

    //拖动scrollView时 隐藏 地址提醒相关内容
    self.addressTooltipView.hidden = YES;
    [FNFreshUser shareInstance].addrWarn = nil;
    if (self.isShowSaleGuideTip) {
        [self saleGuideTipDisappear];
    }
}

- (void)scrollViewDidEndScrollingAnimation:(UIScrollView *)scrollView {
    if ([scrollView isKindOfClass:[UITableView class]]) {
        CGFloat YOffect = scrollView.contentOffset.y;
        CGFloat height = CGRectGetHeight(self.keywordsTabelView.bounds);

        if (YOffect >= scrollView.contentSize.height - height) {
            [scrollView setContentOffset:CGPointMake(0, height) animated:NO];
        }
        if ((int)YOffect % (int)height != 0) {
            int count = (int)YOffect / (int)height + 1;
            [scrollView setContentOffset:CGPointMake(0, height * count) animated:NO];
        }
    } else {
        // 判断是否滑动到底了
        if (self.isScrollToFeeds) {
            self.isScrollToFeeds = NO;
            CGPoint offSetPoint = CGPointMake(0, scrollView.contentSize.height - scrollView.bounds.size.height + 100);
            [scrollView setContentOffset:offSetPoint animated:YES];
        }
    }
}

- (BOOL)scrollViewShouldScrollToTop:(UIScrollView *)scrollView {
    self.cannotScroll = NO;
    [self feedsFlowScrollToTop];
    return YES;
}

#pragma mark - 鲜特卖引导提示

- (void)delayPerformSelector {

    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(saleGuideTipDisappear) object:nil];
    [self performSelector:@selector(saleGuideTipDisappear) withObject:nil afterDelay:2.2];
}

- (void)saleGuideTipAppearCompleted:(void(^)(void))completed {
    self.view.userInteractionEnabled = NO;
    self.freshSaleGuideTip.hidden = NO;
    __weak typeof(self)weakSelf = self;
    [UIView animateWithDuration:0.25 animations:^{

        weakSelf.freshSaleGuideTip.transform = CGAffineTransformIdentity;
        weakSelf.view.userInteractionEnabled = YES;
        completed();
    }];
}

- (void)saleGuideTipDisappear {
    self.freshSaleGuideTip.hidden = YES;
    self.showSaleGuideTip = NO;
}

#pragma mark - FNMerchandiseAddToCartHandlerDelegate

- (void)addToShopCardAnmation:(FNFreshMerchandiseAddToCartResultModel *)resultModel isToast:(BOOL)isToast {
    __weak typeof (self)weakSelf = self;
    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    UIImageView *animateImg = [[UIImageView alloc] initWithFrame:self.addToCardImgRect];
    [animateImg setImage:self.addToCardImgView?:[UIImage imageNamed:@"pic_placeholder"]];
    animateImg.layer.cornerRadius = 55;
    animateImg.layer.masksToBounds = YES;
    [keyWindow addSubview:animateImg];

    UITabBar *tabBar = [[FNFreshTabBarController shareInstance] tabBar];
    UIImageView *shopCartTabIcon = [[FNFreshTabBarController shareInstance] shopCartTabIcon];
    CGPoint oftenBuyTabPoint = [tabBar convertPoint:[FNFreshTabBarController shareInstance].shopCartTabIconCenter toView:keyWindow];
    UILabel *badgeLabel = [FNFreshTabBarController shareInstance].badgeLabel;

    // 动画
    [UIView animateWithDuration:0.3 animations:^{
        [animateImg setFrame:CGRectMake(animateImg.frame.origin.x + 30, animateImg.frame.origin.y -150, 110, 110)];
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.5 animations:^{
            CGAffineTransform  rotate = CGAffineTransformMakeRotation(M_PI/2);
            [animateImg setTransform:rotate];

            [animateImg setFrame:CGRectMake(0,0,40, 40)];
            animateImg.center = oftenBuyTabPoint;

        } completion:^(BOOL finished) {
            [animateImg removeFromSuperview];
            weakSelf.addToCardImgView = nil;
            weakSelf.addToCardImgRect = CGRectZero;
            [weakSelf addShopCarNum:resultModel isToast:isToast];
            //购物车TabIcon 缩放动画
            [shopCartTabIcon iconTransformAnimationWithScale:1.2
                                                    duration:0.2
                                                   completed:nil];
            [badgeLabel iconTransformAnimationWithScale:1.2
                                               duration:0.2
                                              completed:nil];
        }];
    }];
}

- (void)addShopCarNum:(FNFreshMerchandiseAddToCartResultModel *)resultModel isToast:(BOOL)isToast {
    if (isToast) {
        [self startShoppingCartProgressText:resultModel.addCartPromptMsg];
    }
}

// 开始请求规格信息
- (void)addToCartHandlerDidStartRequestSpecification:(FNFreshMerchandiseAddToCartHandler *)handler {
    [self startProgress];
}

// 请求规格信息完成
- (void)addToCartHandlerDidCompleteRequestSpecification:(FNFreshMerchandiseAddToCartHandler *)handler error:(NSError *)error {
    [self stopProgress];
    if (error) {
        NSString *errStr = [error.userInfo safeObjectForKey:NSLocalizedDescriptionKey];

        [self startProgressText:errStr];
    }
}

- (void)addToCartHandlerDidStartAddToShopCart:(FNFreshMerchandiseAddToCartHandler *)handler {
    [self startProgress];
}

- (void)addToCartHandlerDidCompleteAddToShopCart:(FNFreshMerchandiseAddToCartHandler *)handler resultModel:(FNFreshMerchandiseAddToCartResultModel *)resultModel {

    [self stopProgress];

    switch (resultModel.resultType) {
        case FNMerchandiseAddToCartResultTypeSuccess:
        {
            [self addToShopCardAnmation:resultModel isToast:NO];
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Warc-performSelector-leaks"
            if (self.addShoppingCartCell && [self.addShoppingCartCell respondsToSelector:@selector(updateBadge:)]) {
                [self.addShoppingCartCell performSelector:@selector(updateBadge:) withObject:[NSString stringWithFormat:@"%ld",resultModel.goodsCount]];
#pragma clang diagnostic pop
            }
            //            if (self.addToCartModel && [self.addToCartModel isMemberOfClass:[FNMrFreshGoodsModel class]]) {
            //                if (self.isFeeds) {
            //                    ((FNMrFreshGoodsModel *)self.addToCartModel).purchasedNum = [NSString stringWithFormat:@"%ld",resultModel.goodsCount];
            //                } else {
            //                    ((FNMrFreshGoodsModel *)self.addToCartModel).goodsToShopCart.purchasedNum = [NSString stringWithFormat:@"%ld",resultModel.goodsCount];
            //                }
            //            }
        }
            break;
        case FNMerchandiseAddToCartResultTypeFailure:
        {
            [self startProgressText:resultModel.addCartPromptMsg];
        }
            break;
        case FNMerchandiseAddToCartResultTypeLimitPurchase:
        {
            [self startProgressText:resultModel.addCartPromptMsg delay:1.5];
            WS(weakSelf);
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)),
                           dispatch_get_main_queue(), ^{
                [weakSelf addToShopCardAnmation:resultModel
                                        isToast:NO];
            });
        }
            break;
        default:
            break;
    }
}

#pragma mark - IBActions

//练习账号长按地址栏跳转门店选择页隐藏入口
- (IBAction)chooseStoreList:(id)sender {
    if ([FNFreshUser shareInstance].isPractice && [FNFreshUser shareInstance].isLogin) {
        UIViewController *viewController = [[FNMediator sharedInstance] getFreshChangeStoreModele_ChangeStoreViewControllerWithParameter:nil];
        [[FNFreshTabBarController shareInstance] pushViewController:viewController animated:YES];
    }
}
//选择地址页
- (IBAction)presentPositioningViewController:(id)sender {

    UIViewController *viewController = [[FNMediator sharedInstance] freshSelectLocationAddressModule_SelectAddressControllerWithWithParameter:nil freshAddressDidSelectBlock:nil];
    [self presentViewController:viewController animated:YES completion:nil];
    [FNFreshAgent eventWithTrackDataPrameters: @{
        @"page_col":@"100056",
        @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
        @"track_type":@"2",
    }];
}

//搜索页
- (IBAction)presentSearchViewController:(id)sender {

    UIViewController *viewController = [[FNMediator sharedInstance] freshSearchModule_FNFreshSearchViewController_InitWithKeyword:self.keywordLabel.text];

    [[FNFreshTabBarController shareInstance] pushViewController:viewController animated:NO];
    [FNFreshAgent eventWithTrackDataPrameters: @{
        @"page_col":@"100057",
        @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
        @"track_type":@"2",
    }];
}

//消息中心
- (IBAction)presentMessageCenterViewController:(id)sender {
    // 未登录状态直接跳登录页面
    if (![FNFreshUser shareInstance].isLogin) {
        __weak typeof(self)weakSelf = self;
        [FNFreshTarget_LoginModule_Helper login_initWithComplete:^{
            UIViewController *viewController = [[FNMediator sharedInstance] messageModule_FNFreshMessageCenterViewController_Init];
            [weakSelf.navigationController pushViewController:viewController animated:YES];
            [FNFreshAgent eventWithTrackDataPrameters:@{
                @"page_col":@"116025",
                @"page_id" :[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                @"track_type":@"2",
            }];
        }];
        return;
    }
    [self.messageCountLab setHidden:YES];
    UIViewController *viewController =
    [[FNMediator sharedInstance] messageModule_FNFreshMessageCenterViewController_Init];
    [FNFreshTabBarController pushViewController:viewController animated:YES];
    [FNFreshAgent eventWithTrackDataPrameters:@{
        @"page_col":@"116025",
        @"page_id" :[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
        @"track_type":@"2",
    }];
}

// 扫码页
- (IBAction)gotoScanCoupon:(id)sender {

    WS(weakSelf)
    FNSCanViewController *vc =
    [FNSCanViewController freshScanWithBtnTagTypeBlock:^(FNButtonTagStyle type,
                                                         NSString *content,
                                                         FNSCanViewController *vc,
                                                         GetEnterAnotherPageStatus isEnterStatusBlock)
     {
        if (type == FNScanApear && content.length > 0) {
            [weakSelf requestHomeScan:content];
        }
    }];
    vc.explainText = @"可支持识别二维码、商品码、购物卡条码、优惠券充值码";
    vc.fnPreferNavigationBarHidden = YES;
    [self.navigationController pushViewController:vc animated:YES];
    self.scanVC = vc;
    //埋点
    [FNFreshAgent eventWithTrackDataPrameters: @{
        @"page_col":@"146033",
        @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
        @"track_type":@"2",
    }];
}

// 切店
- (IBAction)switchStoreList:(id)sender {
    [self handleSwitchStoreList];
    [FNFreshAgent eventWithTrackDataPrameters: @{
        @"page_col":@"146030",
        @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
        @"track_type":@"2",
    }];
    [FNFreshAgent eventWithTrackDataPrameters: @{
        @"page_col":@"146031",
        @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
        @"track_type":@"6",
    }];
}

// 处理切店
- (void)handleSwitchStoreList {
    if (self.storeListData.stores.count <= 1) {
        return;
    }
    FNFreshMrFreshPopStoreListViewController *viewController = [FNFreshMrFreshPopStoreListViewController instanceWithData:self.storeListData handler:^(FNFreshShopInfoModel *shopInfo) {
        [[FNFreshUser shareInstance] holdShopInfomationWithShopInfoModel:shopInfo errorType:0 needFresh:false];
    }];
    CGFloat width = 325*Ratio;
    NSInteger count = MIN(self.storeListData.stores.count, 4);
    CGFloat height = 102 * Ratio + 45 + count * 86 * Ratio;
    __weak typeof(viewController)weakVC = viewController;
    [self fn_presentViewController:viewController customAnimateType:FNFreshMrFreshCustomAnimateTypeFromCenterKickBack viewSize:CGSizeMake(width, height) duration:0.75 alpha:0.5 handle:^{
        [weakVC dismissViewControllerAnimated:YES completion:nil];
    }];
}


//嵌入飞牛app时 用到
- (IBAction)backFeiniuApp:(id)sender {
    [[FNFreshTabBarController shareInstance].navigationController popViewControllerAnimated:YES];
}


#pragma mark - public methods

//在首页点击tabbar首页icon时要返回到顶部
- (void)scrollToTop {

    if (!self.isCurrentPage) {

        return;
    }
    self.cannotScroll = NO;
    if ([FNFreshMrFreshConstantCacheHandler shareInstance].isEnlargeMode) {
        self.collectionView.scrollEnabled = YES;
    }
    [self feedsFlowScrollToTop];
    [self.collectionView setContentOffset:CGPointZero animated:YES];
    if (self.isRocketShow) {
        [FNFreshAgent eventWithTrackDataPrameters: @{
            @"page_col":@"127090",
            @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
            @"track_type":@"2",
        }];
    }
}

- (void)scrollToFeeds {
    if (!self.isCurrentPage) {
        return;
    }
    //拖动scrollView时 隐藏 地址提醒相关内容
    self.addressTooltipView.hidden = YES;
    [FNFreshUser shareInstance].addrWarn = nil;
    if (self.isShowSaleGuideTip) {
        [self saleGuideTipDisappear];
    }
    // 获取内容的高度和当前collectionView的高度
    CGFloat contentHeight = self.collectionView.contentSize.height + 200;
    CGFloat collectionViewHeight = self.collectionView.bounds.size.height;

    // 如果内容的高度大于collectionView的高度，才能滚动到底部
    if (contentHeight > collectionViewHeight) {
        self.isScrollToFeeds = YES;
        CGPoint bottomOffset = CGPointMake(0, contentHeight - collectionViewHeight);
        [self.collectionView setContentOffset:bottomOffset animated:YES];
    }
}

/// 线下切回线上首页定位不在门店范围内
- (void)setOutOfScopeErrorWithAddress:(NSString *)address isMini:(BOOL)isMini {
    [self setOutOfScopeErrorWithAddress:address isMini:isMini errorInfo:nil];
}

- (void)setOutOfScopeErrorWithAddress:(NSString *)address isMini:(BOOL)isMini errorInfo:(NSString *)errorInfoStr {
    if (self.emptyScrollView) {
        [self.emptyScrollView removeFromSuperview];
        self.emptyScrollView = nil;
    }
    self.isNewOrMiniError = YES;
    [self.emptyNavBgView setHidden:NO];
    [self.addressTooltipView setHidden:YES];
    [self.normalNavigationBar setHidden:YES];
    // 悬浮广告显隐藏
    [self.hangAdImgView setHidden:YES];
    if (self.faultToleranceView) {
        [self removeFaultToleranceView];
    }
    if ([FNFreshMrFreshConstantCacheHandler shareInstance].isRestShow) {
        self.restViewHeightConstraint.priority = 752;
        self.restView.hidden = YES;
        self.emptyNavBgViewHeightConstraint.constant = topBarHeight();
    }
    if (isFNFreshTarget) {
        self.emptyNavTitleLab.text = @"大润发优鲜";
    } else {
        self.emptyNavTitleLab.text = @"欧尚";
    }
    if (address == nil || [address isEqualToString:@""]) {
        address = [FNFreshUser shareInstance].deliveryAddress ?: @"";
    }
    WS(weakSelf)
    FNFreshMrFreshSwitchLocationaddrView *customView = [FNFreshMrFreshSwitchLocationaddrView initWithAddress:address handler:^{
        UIViewController *viewController = [[FNMediator sharedInstance] freshSelectLocationAddressModule_SelectAddressControllerWithWithParameter:nil freshAddressDidSelectBlock:nil];
        [weakSelf presentViewController:viewController animated:YES completion:nil];
        [FNFreshAgent eventWithTrackDataPrameters: @{
            @"page_col":@"100056",
            @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
            @"track_type":@"2",
        }];
    }];
    [self.emptyScrollView addSubview:customView];
    [customView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.emptyScrollView.mas_centerX);
        make.top.equalTo(self.emptyScrollView.mas_top).mas_offset(35 * Ratio);
        make.width.mas_equalTo(SCREEN_WIDTH - 6);
        make.height.mas_equalTo(40 * Ratio);
    }];

    NSString *title = FNFreshMrFreshNativePracticeErrorTips;
    if (isMini) {
        title = FNFreshMrFreshNativeMiniAndCloseErrorTips;
    }
    if (errorInfoStr && ![errorInfoStr isEqualToString:@""]) {
        title = errorInfoStr;
    }
    [self fnFresh_addDefaultEmptyDataWithoutButtonSetWithTarget:self.emptyScrollView image:[UIImage fnFresh_imageNamed:@"pic_empty_3"] title:title];

    [self.emptyScrollView bringSubviewToFront:customView];
}

- (void)scanOpenPageWithURLString:(NSString *)URLString {
    FNFreshUrlRouter *URLRouter = [FNFreshUrlRouter shared];
    URLRouter.source = FNFreshUrlRouterSourceScan;
    [URLRouter jumpControllerWithRemoteURLString:URLString completion:nil];
}

- (void)openPageWithURLString:(NSString *)URLString {
    [self openPageWithURLString:URLString retryCount:0];
}

- (void)openPageWithURLString:(NSString *)URLString retryCount:(NSInteger)retryCount {
    // 检查基本条件
    if (!URLString || URLString.length == 0) {
        return;
    }

    // 检查navigationController状态
    FNFreshTabBarController *tabBarController = [FNFreshTabBarController shareInstance];
    if (!tabBarController.navigationController) {
        // 如果navigationController为空，延迟重试
        if (retryCount < 3) {
            __weak typeof(self) weakSelf = self;
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [weakSelf openPageWithURLString:URLString retryCount:retryCount + 1];
            });
        }
        return;
    }

    FNFreshUrlRouter *URLRouter = [[FNFreshUrlRouter alloc] init];
    [URLRouter jumpControllerWithRemoteURLString:URLString completion:nil];
}

#pragma mark - pritave methods
// 设置顶部导航栏UI渐变
- (void)setNavigationBarUIAnimate:(CGFloat)YOffect {
    CGFloat rise = YOffect < 0 ? 0 : MIN(1, YOffect / 70);
    CGFloat fall = 1 - rise;
    riseIsChange = compareRise != rise;
    if (riseIsChange) {
        compareRise = rise;
        self.searchBackgroundView.alpha = rise;

        BOOL isRest = self.viewModel.responseModel.storeStatus == 1;
        if (rise <= 0) {
            self.normalNavigationViewHeightConstraint.constant = (isRest?0:kStatusBarHeight) + FNMrFreshTopNormalNavigationBarHeight;
        } else {
            self.normalNavigationViewHeightConstraint.constant = (isRest?0:kStatusBarHeight) + 44 + 39 * fall;
        }
        if (rise >= 1) {
            self.isNormalBgNavi = YES;
            [self.messageView setHidden:YES];
            [self.changeStoreView setHidden:YES];
            [self.locationView setHidden:YES];
            [self.searchBtn setBackgroundColor:[UIColor hex:@"#E60012"]];
        } else {
            self.isNormalBgNavi = NO;
            [self.messageView setHidden:NO];
            [self.locationView setHidden:NO];
            [self.changeStoreView setHidden:NO];
            if ((self.viewModel.responseModel.bgColor.length > 0)) {
                [self.searchBtn setBackgroundColor:[UIColor hex:self.viewModel.responseModel.bgColor]];
            }
        }

        if (!self.isShowMsgCenter) {
            [self.messageView setHidden:YES];
        }
        if (self.storeListData.stores.count <= 1) {
            [self.changeStoreView setHidden:YES];
        }
    }

    if (YOffect < 0) {
        CGFloat risePrecent = 1 - MIN(1, -YOffect / 40.0);
        self.normalNavigationBar.alpha = risePrecent;
    } else {
        self.normalNavigationBar.alpha = 1;
    }
}

- (void)setup {
    self.collectionView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
    self.restContentViewTopConstraint.constant = statusBarHeight;

    //    self.collectionView.backgroundColor = [UIColor fn_colorWithColorKey:kFNViewBgKey];
    self.fnPreferNavigationBarHidden = YES;
    self.firstEntry = YES;
    self.firstEntryShop = YES;
    self.edgesForExtendedLayout = UIRectEdgeTop;
    self.view.backgroundColor = [UIColor fn_colorWithColorKey:kFNViewBgKey];

    self.keywordLabel.font = [UIFont systemFontOfSize:14.f*Ratio];
    self.keywordLabel.textColor = [UIColor fn_colorWithColorKey:kFN999999];

    [self.searchBackgroundView setColors:@[(id)[UIColor hex:@"F64139"].CGColor,
                                           (id)[UIColor hex:@"E50113"].CGColor]];
    [self.emptyNavBgView setColors:@[(id)[UIColor hex:@"F64139"].CGColor,
                                     (id)[UIColor hex:@"E50113"].CGColor]];

    [self.restView setColors:@[(id)[UIColor hex:@"f8e71c"].CGColor,
                               (id)[UIColor hex:@"f8ce1c"].CGColor]];
    [self setupAddressTooltipView];

    // 设置banner氛围背景
    self.bannerBgImgView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 397)];
    self.bannerBgImgView.layer.zPosition = -100;
    self.bannerBgImgView.layer.masksToBounds = YES;
    self.bannerBgImgView.contentMode = UIViewContentModeBottom;
    [self.collectionView addSubview:self.bannerBgImgView];
}

- (void)setupAddressTooltipView {
    WS(weakSelf);
    FNFreshMrFreshTooltipView *tooltipView = [[FNFreshMrFreshTooltipView alloc]init];
    tooltipView.hidden = YES;
    [self.view addSubview:tooltipView];
    [tooltipView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(12);
        make.top.mas_equalTo(weakSelf.normalNavigationBar.mas_bottom).offset(-50);
        make.right.mas_lessThanOrEqualTo(weakSelf.view.mas_centerX);
    }];
    tooltipView.userInteractionEnabled = YES;

    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(presentPositioningViewController:)];
    [tooltipView addGestureRecognizer:tap];

    self.addressTooltipView = tooltipView;
}

- (void)addObserver {
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(reloadDataWithType:) name:kFNFreshAddressChangedNotification object:nil];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(locationInformation) name:kFreshLocationNotification object:nil];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(loginStateDidChange) name:kFNFreshLoginStateDidChangeNotification object:nil];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(reloadData) name:kFNFreshRefreshHomeDataNotification object:nil];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(shopDidChanged) name:kFNFreshShopDidChangedNotification object:nil];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(scrollToTop) name:kFNFreshScrollToTopNotification object:nil];
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(reloadTableView:) name:FNMrFreshKeywordsChangeNotificationName object:nil];
    __weak typeof(self)weakSelf = self;
    [self.KVOController observe:[FNFreshUser shareInstance] keyPath:@"isLogin" options:NSKeyValueObservingOptionNew block:^(FNFreshMrFreshViewController *observer, FNFreshUser *object, NSDictionary *change) {

        if (!object.isLogin) {

            weakSelf.haveShowedCouponWindow = 0;
        }
    }];
}

- (void)changeTimerState:(BOOL)state {

    for (UICollectionViewCell *cell in self.collectionView.visibleCells) {

        if ([cell.reuseIdentifier isEqualToString:FNFreshMrFreshShufflingBannerCollectionViewCellIdentifier]) {

            [(FNFreshMrFreshShufflingBannerCollectionViewCell *)cell changeTimerState:state];
        } else if ([cell.reuseIdentifier isEqualToString:FNFreshMrFreshManagerRecommendCollectionViewCellIdentifier]) {

            [(FNFreshMrFreshManagerRecommendCollectionViewCell *)cell changeTimerState:state];
        } else if ([cell.reuseIdentifier isEqualToString:FNFreshMrFreshAnnouncementCollectionViewCellIdentifier]) {
            [(FNFreshMrFreshAnnouncementCollectionViewCell *)cell changeTimerState:state];
        }
    }
}

//上拉加载  下拉刷新
- (void)addRefreshGestures {

    if (!self.collectionView.mj_header) {

        __weak typeof(self)weakSelf = self;
        MJRefreshComponent *header;

        if (isFNFreshTarget) {
            header = [FNFreshNewHomeRefreshHeader headerWithRefreshingBlock:^{
                if (weakSelf.isRefreshing) {
                    [weakSelf.collectionView.mj_header endRefreshing];
                    return ;
                }
                [FNFreshAgent eventWithTrackDataPrameters: @{
                    @"page_col":@"208001",
                    @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                    @"track_type":@"2"
                }];
                [weakSelf requestMrFreshData];
            }];
        } else {
            header = [FNFreshNewACToGoHomeRefreshHeader headerWithRefreshingBlock:^{
                if (weakSelf.isRefreshing) {
                    return ;
                }
                [weakSelf requestMrFreshData];
            }];
        }

        header.beginRefreshingCompletionBlock = ^{
            weakSelf.refreshing = YES;
            [weakSelf changeTimerState:NO];
        };
        header.endRefreshingCompletionBlock = ^{
            weakSelf.refreshing = NO;
            weakSelf.normalNavigationBar.hidden = NO;
            weakSelf.normalNavigationBar.alpha = 1;
            [weakSelf changeTimerState:YES];
        };
        self.collectionView.mj_header = (MJRefreshHeader *)header;
    };
    self.collectionView.mj_footer = nil;
}

- (void)removeRefreshGestures {
    self.collectionView.mj_header = nil;
    self.collectionView.mj_footer = nil;
}

#ifdef ISFRAMEWORK
#else
- (void)checkADPageViewController {
    __weak typeof(self)weakSelf = self;
    void(^block)(void) = ^{
        weakSelf.leaveADPageVC = YES;
        [weakSelf requestForVersionUpdate];
    };
    FNFreshADPageViewController *viewController = [FNFreshADPageViewController existInstance];
    if (viewController) {
        [viewController addBlockWith:block];
    } else {
        block();
    }
}

#endif

- (void)locationInformation {
    FNFreshUser *user = [FNFreshUser shareInstance];
    if (user.locationFinished) {
        [self requestIsPractice];
        [self stopProgress];
        __weak typeof(self)weakSelf = self;
        switch (user.enterHomeType) {
            case FNFreshEmptyType:
                if (self.isEmpty) {
                    [self startProgressText:user.locationError.localizedDescription];
                } else {
                    [self addEmptyViewWithError:user.locationError event:^{

                        [weakSelf startProgressInView:weakSelf.collectionView];
                        [[FNFreshLocationLogic shareInstance] openPositionWithUrl:nil payloadUrl:nil];
                    }];
                }
                [self removeRefreshGestures];
                break;

            case FNFreshLocationOnlineStoreType:
            case FNFreshOffLineStoreType:
                //因为改变地址也会刷新首页数据,为了避免二次刷新,故在这里不在刷新
                break;

                //容错页 note: v178-不在返回游客容错页
            case FNFreshDefaultCityType:
                // 第一次安装app，定位权限弹框---默认背景容错页
                if (![[FNCacheManager shareMananger] firstOpenForPage:FNMrFreshFirstEnterAppWithFaultViewFromNot]) {
                    [[FNCacheManager shareMananger] setFirstOpenedForPage:FNMrFreshFirstEnterAppWithFaultViewFromNot];
                    if (self.faultToleranceView) {
                        return;
                    }
                }

                [self addFaultToleranceView];
                [self removeEmptyView];
                if (!self.isPopupWindowInFaulTolerance) {
                    [self requestPopupWindowInfo];
                }
                break;
            default:
                break;
        }
    }
}

- (void)addFaultToleranceView {
    [self addH5WebViewWithLinkUrl:[FNFreshUser shareInstance].pushH5LinkUrl];
}

- (void)addH5WebViewWithLinkUrl:(NSString *)linkUrl {

    __weak typeof(self)weakSelf = self;
    if (self.faultToleranceView) {

        [self removeFaultToleranceView];
    }
    FNFreshMrFreshFaultToleranceView *view = [FNFreshMrFreshFaultToleranceView instanceWithURLString:linkUrl shouldLoad:!self.isCurrentPage handler:^{

        [weakSelf presentPositioningViewController:nil];

        [FNFreshAgent eventWithTrackDataPrameters: @{
            @"page_col":@"148037",
            @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
            @"track_type":@"2",
        }];
    }];
    [self.view addSubview:view];
    [view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.bottom.equalTo(self.view);
    }];
    [[FNFreshTabBarController shareInstance] hideTabbar:YES];

    self.view.frame = [FNFreshTabBarController shareInstance].view.bounds;
    self.faultToleranceView = view;
    [self setNeedsStatusBarAppearanceUpdate];
    [[FNFreshTabBarController shareInstance] hiddenTabbarTipReminderView];
}

- (void)removeFaultToleranceView {

    [self.faultToleranceView removeFromSuperview];
    self.faultToleranceView = nil;
    [[FNFreshTabBarController shareInstance] hideTabbar:NO];
    CGFloat tabbarHeight = isHairIPhone ? (50.f + 33.f) : 50.f;
    self.view.frame = CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT - tabbarHeight);
    [self setNeedsStatusBarAppearanceUpdate];
}

- (void)setShopCartWithImageView:(UIImageView *)imgView andGoodsModel:(FNMrFreshGoodsModel *)goodsModel source:(NSString *)source {
    self.addToCardImgView = imgView.image;
    self.addToCardImgRect = [[UIApplication sharedApplication].keyWindow convertRect:imgView.frame fromView:imgView];
    [self addToShopCartWithGoodsModel:goodsModel source:source];
}

- (void)addToShopCartWithGoodsModel:(FNMrFreshGoodsModel *)goodsModel source:(NSString *)source {

    void(^block)(void) = ^{
        FNFreshMerchandiseAddToCartItemModel *itemModel = [[FNFreshMerchandiseAddToCartItemModel alloc] init];
        itemModel.merchandiseId = goodsModel.goodsID;
        itemModel.qty = goodsModel.goodsToShopCart.needBuyQty.integerValue;
        itemModel.kind = goodsModel.goodsToShopCart.kind.integerValue;
        itemModel.campaignSeq = goodsModel.goodsToShopCart.activityId;
        itemModel.source = source;
        itemModel.isVoucherGoods = goodsModel.isVoucherGoods;

        FNShopCartMerchantInfo *merchantStoreInfo = [[FNShopCartMerchantInfo alloc] init];
        merchantStoreInfo.channelStoreId = goodsModel.channelStoreId;
        merchantStoreInfo.merchantCode = goodsModel.merchantCode;
        merchantStoreInfo.merchantType = goodsModel.merchantType;
        merchantStoreInfo.saleStoreId = goodsModel.saleStoreId;
        merchantStoreInfo.merchantStoreId = goodsModel.merchantStoreId;

        itemModel.merchantStoreInfo = merchantStoreInfo;

        FNMerchandiseAddToCartExtraModel *extraModel = [FNMerchandiseAddToCartExtraModel new];
        extraModel.isPop = goodsModel.isHasOption;
        extraModel.isMultiSpec = [goodsModel.goodsToShopCart.isMultiSpecifition isEqualToString:@"1"];
        extraModel.isPairItem = goodsModel.isPairItem.boolValue;
        extraModel.shield = goodsModel.shield;
        [self.addToCartHandler addToShopcartWithParameterItemArray:(@[itemModel]) extra:extraModel];
    };
    if ((goodsModel.newComeIdent || goodsModel.goodsToShopCart.isLimit.integerValue) && ![FNFreshUser shareInstance].isLogin) {
        [FNFreshTarget_LoginModule_Helper login_initWithComplete:block];
    } else {
        block();
    }
}

- (void)reloadDataWithType:(NSNotification *)not {
    self.errorType = [not.object integerValue];
    [self reloadData];
}

- (void)reloadData {
    self.locationPermissionView.hidden = [self locationPermissionViewHidden];
    [[FNFreshTabBarController shareInstance] hideTabbar:![self locationPermissionViewHidden]];
    {
        /** 这里由于hideTabbar 导致view size 不准确，这里手动校准*/
        CGRect currentFrame = self.view.frame;
        currentFrame.size.height = SCREEN_HEIGHT - [FNFreshTabBarController shareInstance].tabBar.frame.size.height;
        self.view.frame = currentFrame;
    }
    if (self.faultToleranceView) {
        [self removeFaultToleranceView];
    }
    [self removeEmptyView];
    self.addressLab.text = [FNFreshUser shareInstance].deliveryAddress ?: @"";
    if ((![FNFreshUser shareInstance].isPractice && [FNFreshUser shareInstance].isNewStore) || [FNFreshUser shareInstance].storeType == 3) {
        if (!self.isNewOrMiniError) {
            [self setOutOfScopeErrorWithAddress:nil isMini:[FNFreshUser shareInstance].storeType == 3];
        }
        return;
    }
    if (self.firstEntry && [FNFreshUser shareInstance].gpsAddressResponseModel.showType == 5) {
        // 第一次全局定位进入首页，判断showType是否为5，表示展示为开业容错页
        [self setOutOfScopeErrorWithAddress:nil isMini:NO errorInfo:[FNFreshUser shareInstance].faultTolerantPageInfo];
        [self.bottomAddressView removeFromSuperview];
        self.firstEntry = NO;
        return;
    }
    if ([FNFreshUser shareInstance].faultTolerantStore == 1) {
        [self.view addSubview:self.bottomAddressView];
        [self.view bringSubviewToFront:self.bottomAddressView];
        [self.bottomAddressView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.bottom.leading.trailing.mas_equalTo(self.view);
            make.height.mas_equalTo(44);
        }];
        [FNFreshAgent eventWithTrackDataPrameters: @{
            @"page_col":@"200036",
            @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
            @"track_type":@"6",
        }];
        [self.qualificationView setHidden:YES];
    } else {
        [self.bottomAddressView removeFromSuperview];
    }
    [self requestMrFreshData];
}

- (void)loginStateDidChange {
    self.addressLab.text = [FNFreshUser shareInstance].deliveryAddress ?: @"";
    if ([FNFreshUser shareInstance].isLogin) {
        [self requestForSendGiftOnLoad];
    } else {
        [self removeOrderStateView];
    }
    //    if (self.isFirstEntry) {
    //        return;
    //    }
    [self reloadData];
    /// 全球购相关用户信息刷新
    [[FNMediator sharedInstance] loginStateDidChangeForSQShop:[FNFreshUser shareInstance].isLogin complete:nil];
}

- (void)shopDidChanged {
    self.firstEntryShop = YES;
    self.popupWindowInFaulTolerance = NO;
}

- (void)reloadCollectionViewData {

    __weak typeof(self)weakSelf = self;
    [UIView performWithoutAnimation:^{
        [weakSelf.collectionView reloadData];
    }];
    if (self.isRefreshing) {
        return;
    }
    self.collectionView.contentOffset = CGPointZero;
    self.isRocketShow = NO;
}

- (void)presentScanCouponSuccessPopVcWithModel:(FNFreshCouponRechargeResponseModel *)model {
    FNFreshMyCouponAlertViewController *vc = [[FNFreshMyCouponAlertViewController alloc] initWithNibName:NSStringFromClass([FNFreshMyCouponAlertViewController class]) bundle:nil];
    vc.contentText = model.noticeMsgNew;
    __weak typeof(vc)weakVC = vc;
    __weak typeof(_scanVC)weakScanVC = _scanVC;
    vc.handler = ^{
        [weakScanVC startScanRunning];
    };
    [self fn_presentViewController:vc customAnimateType:FNFreshMrFreshCustomAnimateTypeFadeInFadeOut viewSize:CGSizeMake(270, 194) duration:0.25 alpha:0.5 handle:^{
        [weakVC dismissViewControllerAnimated:YES completion:nil];
        [weakScanVC startScanRunning];
    }];
}

- (void)addEmptyViewWithError:(NSError *)error event:(void(^)(void))event {

    if ([FNFreshUser shareInstance].deliveryAddress.length > 0) {
        self.emptyAccessoryLabel.text = [FNFreshUser shareInstance].deliveryAddress;
    } else if (error.code == FNNetworkErrorNoNetworking) {
        self.emptyAccessoryLabel.text = FNMrFreshNoNetwork;
    } else {
        self.emptyAccessoryLabel.text = FNMrFreshLocationFailure;
    }
    self.empty = YES;
    [self switchNavigationBarStyle:FNMrFreshNavigationBarStyleAbnormal];
    [self fnFresh_setVerticalOffsetForEmptyDataSet:80 * Ratio];
    [self fnFresh_addNetworkErrorEmptyDataSetWithTarget:self.collectionView errorCode:@(error.code) errorDesc:error.localizedDescription refreshEvent:event];
}

- (void)removeEmptyView {

    [self switchNavigationBarStyle:FNMrFreshNavigationBarStyleNormal];
    [self fnFresh_clearEmptyView:self.collectionView];
    self.empty = NO;
}

- (void)switchNavigationBarStyle:(FNMrFreshNavigationBarStyle)style {
    
    switch (style) {
        case FNMrFreshNavigationBarStyleNormal:
            if (!self.isRefreshing) {
                self.normalNavigationBar.hidden = NO;
            }
            self.abnormalNavigationBar.hidden = YES;
            break;

        case FNMrFreshNavigationBarStyleAbnormal:
            self.normalNavigationBar.hidden = YES;
            self.abnormalNavigationBar.hidden = NO;
            break;
    }
    [self setNeedsStatusBarAppearanceUpdate];
}

//文本内容是定位接口所给  但是需求需要首页正常显示才会显示
- (void)displayRemindViewWithText:(NSString *)text {

    //注意当前地址
    self.addressTooltipView.text = text;
    self.addressTooltipView.hidden = NO;
}

- (void)showAddressWarning {
    if (!self.versionUpdateFinish || !self.viewModel.responseModel) {
        return;
    }

    NSString *extractedExpr = [FNFreshUser shareInstance].addrWarn;
    if (extractedExpr.length > 0) {
        [self displayRemindViewWithText:extractedExpr];
    } else {
        self.addressTooltipView.hidden = YES;
        [FNFreshUser shareInstance].addrWarn = nil;
    }
}

- (void)setupTopBar {
    self.collectionView.mj_header.backgroundColor = [UIColor clearColor];
    FNFreshMrFreshResponseModel *responseObject = self.viewModel.responseModel;
    BOOL isShowService = responseObject.isShowStoreService;
    CGFloat bannerHeight = floorf((SCREEN_WIDTH - 24) * 116 / 351); //banner高度
    CGFloat serviceHeight = isShowService ? floorf(74.f*Ratio) : 2; // 门店服务栏整体高度
    CGFloat bannerBgHeight = (responseObject.storeStatus ? 0 : kStatusBarHeight) + bannerHeight + serviceHeight + FNMrFreshTopNormalNavigationBarHeight + ceilf(80*Ratio);
    if (responseObject.storeStatus == 1) {
        [(FNFreshNewHomeRefreshHeader *)self.collectionView.mj_header setAnimatorTopPosition:-20];
        [FNFreshMrFreshConstantCacheHandler shareInstance].isRestShow = YES;
        self.restViewHeightConstraint.priority = 749;
        self.restView.hidden = NO;
        self.restLabel.text = responseObject.storeStatusNotice;
        self.restNameLabel.text = responseObject.storeStatusDesc;
        self.abnormalNavigationViewHeightConstraint.constant = navigationBarHeight();
        self.emptyNavBgViewHeightConstraint.constant = navigationBarHeight();
        self.normalNavigationViewHeightConstraint.constant = FNMrFreshTopNormalNavigationBarHeight;
    } else {
        [(FNFreshNewHomeRefreshHeader *)self.collectionView.mj_header resetAnimatorTop];
        [FNFreshMrFreshConstantCacheHandler shareInstance].isRestShow = NO;
        self.restViewHeightConstraint.priority = 752;
        self.restView.hidden = YES;
        self.abnormalNavigationViewHeightConstraint.constant = topBarHeight();
        self.emptyNavBgViewHeightConstraint.constant = topBarHeight();
        self.normalNavigationViewHeightConstraint.constant = FNMrFreshTopNormalNavigationBarHeight + (kStatusBarHeight);
    }

    // 配置地址栏跑马灯数据
    [self performSelector:@selector(requestArriveTime) withObject:nil afterDelay:1];

    // 消息入口显隐藏
    if (self.isShowMsgCenter) {
        [self.messageView setHidden:NO];
        [FNFreshAgent eventWithTrackDataPrameters: @{
            @"page_col":@"153001",
            @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
            @"track_type":@"6",
        }];
    } else {
        [self.messageView setHidden:YES];
    }
    // 悬浮广告显隐藏
    self.hangAdImgView.hidden = !self.viewModel.responseModel.showHangAd;
    WS(weakSelf)
    [self.hangAdImgView setupWithLinkUrl:self.viewModel.responseModel.adImgUrl
                         andSecondImgUrl:nil
                              andHandler:^{
        [weakSelf hangAdImgClick];
    }];

    // 全局背景图片
    NSString *bgImgUrlStr = self.viewModel.responseModel.bgImgUrl;
    if (bgImgUrlStr.length > 0) {
        WS(weakSelf)
        [[SDWebImageManager sharedManager]
         loadImageWithURL:[NSURL URLWithString:bgImgUrlStr]
         options:SDWebImageRetryFailed
         progress:nil
         completed:^(UIImage * _Nullable image, NSData * _Nullable data, NSError * _Nullable error, SDImageCacheType cacheType, BOOL finished, NSURL * _Nullable imageURL) {
            if (!image) {
                return;
            }
            weakSelf.bgImgView.hidden = NO;
            self.configBgView.hidden = YES;
            self.bannerBgImgView.hidden = YES;
            weakSelf.bgImgView.image = image;
            NSUInteger height = floor(SCREEN_WIDTH * image.size.height / image.size.width);
            weakSelf.bgImgViewHeightConstraint.constant = height;
        }];
        self.bgImgViewTopConstraint.constant = 0;

    } else {
        self.bgImgView.hidden = YES;
        self.configBgView.hidden = NO;
        self.bannerBgImgView.hidden = YES;
        [self.configBgView setBackgroundColor:[UIColor hexString:@"#E60012"]];
        self.configBgViewHeightConstriant.constant = bannerBgHeight - 20; // 默认背景高度稍微矮一点
        self.bgImgViewHeightConstraint.constant = bannerBgHeight;
        CGRect frame = self.bannerBgImgView.frame;
        frame.size.height = bannerBgHeight;
        self.bannerBgImgView.frame = frame;

        // 默认 mask遮罩
        CAGradientLayer *defaultMask = [self getMaskLayer:YES];
        CGFloat width = SCREEN_WIDTH;
        CGFloat height = ceilf(80*Ratio);
        defaultMask.frame=CGRectMake(0, self.configBgViewHeightConstriant.constant - height, width, height);
        defaultMask.startPoint = CGPointZero;
        defaultMask.endPoint = CGPointMake(0, 1);
        defaultMask.colors = @[(id)[UIColor colorWithHex:0xF2F2F2 alpha:0].CGColor,
                               (id)[UIColor colorWithHex:0xF2F2F2 alpha:1].CGColor];
        //配置背景 mask遮罩
        CAGradientLayer *bannerBgMask = [self getMaskLayer:NO];
        bannerBgMask.frame=CGRectMake(0, bannerBgHeight - height, width, height);
        bannerBgMask.startPoint = CGPointZero;
        bannerBgMask.endPoint = CGPointMake(0, 1);
        bannerBgMask.colors = @[(id)[UIColor colorWithHex:0xF2F2F2 alpha:0].CGColor,
                                (id)[UIColor colorWithHex:0xF2F2F2 alpha:1].CGColor];
    }

    if (self.viewModel.responseModel.bgColor.length > 0) {
        [self.searchBtn setBackgroundColor:[UIColor hex:self.viewModel.responseModel.bgColor]];
    } else {
        [self.searchBtn setBackgroundColor:[UIColor hex:@"#E60012"]];
    }
    // 设置首页黑白滤镜
    if (self.viewModel.responseModel.showDark && self.isCurrentPage) {
        [[UIApplication sharedApplication] delegate].window.layer.filters = @[self.grayFilter];
    }
    self.isLastGrayFliter = self.viewModel.responseModel.showDark;

    // webview 气氛
    if (self.viewModel.responseModel.atmosphereH5.length > 0 && _currentPage) {
        DFileLog(@"Web-atmosphere h5 url = %@",self.viewModel.responseModel.atmosphereH5);

        /**
         load web's url
         */
        [self.homeWebView loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:self.viewModel.responseModel.atmosphereH5]]];
        [self.homeWebView setHidden:YES];

        NSInteger time = self.viewModel.responseModel.atmosphereH5ShowTimeInterval > 0?self.viewModel.responseModel.atmosphereH5ShowTimeInterval:30;

        WS(weakSelf)
        self.webViewAutoDismissTimer = [NSTimer scheduledTimerWithTimeInterval:time repeats:NO block:^(NSTimer * _Nonnull timer) {
            [weakSelf releaseWebView];
        }];
    }
}

- (CAGradientLayer *)getMaskLayer:(BOOL)isDefault {
    __block CAGradientLayer *maskLayer = [[CAGradientLayer alloc] init];
    if (isDefault) {
        [self.configBgView.layer.sublayers enumerateObjectsUsingBlock:^(__kindof CALayer * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (obj.zPosition == -1000) {
                maskLayer = obj;
                *stop = YES;
            }
        }];
    } else {
        [self.bannerBgImgView.layer.sublayers enumerateObjectsUsingBlock:^(__kindof CALayer * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (obj.zPosition == -1000) {
                maskLayer = obj;
                *stop = YES;
            }
        }];
    }
    if (maskLayer.zPosition != -1000) {
        maskLayer.zPosition = -1000;
        maskLayer.bounds = CGRectMake(0, 0, SCREEN_WIDTH, 80);
        if (isDefault) {
            [self.configBgView.layer addSublayer:maskLayer];
        } else {
            [self.bannerBgImgView.layer addSublayer:maskLayer];
        }
    }
    return maskLayer;
}

/**
 展示堂食入口
 */
- (void)showEatInHomeEntrance {

    self.eatInSuspensionView.hidden = !self.viewModel.eatInResponseModel.showEntrance;
    WS(weakSelf)
    [self.eatInSuspensionView setupWithDataModel:self.viewModel.eatInResponseModel handler:^{
        UIViewController *ordrerForhereVC = [[FNMediator sharedInstance] OrderForHereModule_FNFreshOrderForHereVC_InitializeWith:[NSString stringWithFormat:@"%ld",(long)weakSelf.viewModel.eatInResponseModel.waitMinute]];
        [weakSelf.navigationController pushViewController:ordrerForhereVC animated:YES];

        [FNFreshAgent eventWithTrackDataPrameters: @{@"page_col":@"119001",
                                                     @"page_id":@"3",
                                                     @"track_type":@"2",
                                                   }];
    }];
}

- (NSIndexPath *)bottomIndexPathWithContentoffset:(CGPoint)contentOffset {
    CGPoint point = CGPointMake(contentOffset.x, contentOffset.y + self.collectionView.bounds.size.height);
    return [self.collectionView indexPathForItemAtPoint:point];
}

/**
 出现切换门店按钮时，需要加载气泡提示“多家门店可提供服务，点此切换”，每个自然日出现一次，优先级＞地址栏气泡出现的优先级，3S后消失
 */
- (void)showStoreListSwitchImgTips {
    if (![[FNCacheManager shareMananger] firstOpenForPage:NSStringFromClass([self class])] ||
        !self.isCurrentPage || self.storeListData.stores.count <= 1) {
        return;
    }
    NSDate *date = (NSDate *)[[FNCacheManager shareMananger] cacheObjectWithKey:FNMrFreshSwitchStoreListTipForDay];
    if (date && [self isSameDay:date and:[NSDate date]]) { // 每个自然日只弹一次
        return;
    }
    [[FNCacheManager shareMananger] setCacheObject:[NSDate date] forKey:FNMrFreshSwitchStoreListTipForDay];
    [self.switchStoreListImg setHidden:NO];
    self.addressTooltipView.hidden = YES;
    [FNFreshUser shareInstance].addrWarn = nil;
    [self performSelector:@selector(switchStoreImgDismiss) withObject:nil afterDelay:3];
}

- (void)switchStoreImgDismiss {
    [self.switchStoreListImg setHidden:YES];
}

- (void)reloadTableView:(NSNotification *)not {
    self.keywords = [[FNCacheManager shareMananger] cacheResponseObjectWithKey:FNMrFreshKeywords];
    [self.keywordsTabelView reloadData];
    if (self.keywords.count > 1) {
        if (self.keywordsTabelView.numberOfSections > 0 && [self.keywordsTabelView numberOfRowsInSection:1] > 0) {

            [self.keywordsTabelView scrollToRowAtIndexPath:[NSIndexPath indexPathForRow:0 inSection:1] atScrollPosition:UITableViewScrollPositionTop animated:NO];
        }
        [self createTimer:[not.object integerValue]];
    } else {
        [self invalidateTimer];
    }
}

- (NSInteger)indexWithIndexPath:(NSIndexPath *)indexPath {

    if (self.keywords.count == 1) {

        return indexPath.row;
    } else if (self.keywords.count > 1) {
        NSInteger index = 0;
        switch (indexPath.section) {
            case 0:
                index = self.keywords.count - 1;
                break;
            case 1:
                index = indexPath.row;
                break;
            case 2:
                index = 0;
                break;

            default:
                break;
        }
        return index;
    } else {
        return 0;
    }
}

- (void)createTimer:(NSTimeInterval)second {

    if (self.keywordsTimer.isValid) {
        [self.keywordsTimer invalidate];
        self.keywordsTimer = nil;
    }

    self.keywordsTimer = [NSTimer scheduledTimerWithTimeInterval:second target:self selector:@selector(timerRun) userInfo:nil repeats:YES];
}

- (void)invalidateTimer {

    if (!self.keywordsTimer.isValid) {
        return;
    }
    [self.keywordsTimer invalidate];
    self.keywordsTimer = nil;
}

- (void)timerRun {

    CGFloat height = CGRectGetHeight(self.keywordsTabelView.bounds);
    CGFloat YOffect = self.keywordsTabelView.contentOffset.y;
    [self.keywordsTabelView setContentOffset:CGPointMake(0, YOffect + height) animated:YES];
}

#pragma mark - 资质与协议

- (void)showQualification {
    if (!self.emptyNavBgView.isHidden || [self.bottomAddressView isDescendantOfView:self.view]) {
        [self.qualificationView setHidden:YES];
        return;
    }

    [self.qualificationView setHidden:NO];

    [self.hangAdImgView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.bottom.mas_equalTo(self.qualificationView.mas_top).offset(-5);
    }];
    if (self.orderStateView || self.isShowCouponExpireTip) {
        self.qualificationViewBottomConstraint.constant = 50;
    } else {
        self.qualificationViewBottomConstraint.constant = 15;
    }
}

// 点击资质与协议跳转关于页面
- (IBAction)gotoAboutUsVC:(id)sender {
    FNFreshUserCenterSettingAboutViewController *aboutVC = [[FNFreshUserCenterSettingAboutViewController alloc] init];
    [self.navigationController pushViewController:aboutVC animated:YES];
    [FNFreshAgent eventWithTrackDataPrameters: @{
        @"page_col":@"183003",
        @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
        @"track_type":@"2",
    }];
}

#pragma mark - setters and getters

- (NSDictionary<NSString *,UIImage *> *)bgImgCacheDic {
    if (!_bgImgCacheDic) {
        _bgImgCacheDic = [[NSMutableDictionary alloc] init];
    }
    return _bgImgCacheDic;
}

- (FNFreshMrFreshViewModel *)viewModel {
    if (!_viewModel) {
        __weak typeof(self)weakSelf = self;
        _viewModel = [[FNFreshMrFreshViewModel alloc] initWithHandler:^(NSString *keyword) {
            if (keyword.length > 0) {
                weakSelf.keywordLabel.text = keyword;
            } else {
                weakSelf.keywordLabel.text = @"寻找称心商品";
            }
        }];
    }
    return _viewModel;
}

- (FNFreshMerchandiseAddToCartHandler *)addToCartHandler {
    if (!_addToCartHandler) {
        _addToCartHandler = [[FNFreshMerchandiseAddToCartHandler alloc] initWithDelegate:self];
    }
    return _addToCartHandler;
}

- (FNFreshMrFreshHangAdsView *)hangAdImgView {
    if (!_hangAdImgView) {
        FNFreshMrFreshHangAdsView *view = [[NSBundle mainBundle] loadNibNamed:NSStringFromClass([FNFreshMrFreshHangAdsView class])
                                                                        owner:nil
                                                                      options:nil].firstObject;
        [self.view addSubview:view];
        [view mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(120, 54));
            make.right.equalTo(self.view.mas_right).offset(54);
            make.bottom.equalTo(self.view.mas_bottom).offset(-50);
        }];
        self.hangAdImgView = (FNFreshMrFreshHangAdsView *)view;
    }
    return _hangAdImgView;
}

- (void)hangAdImgClick {
    NSString *linkUrl = self.viewModel.responseModel.adLinkUrl;
    if (linkUrl != nil && ![linkUrl isEqualToString:@""]) {
        FNFreshFirstShowGiftParameterModel *parameter = [[FNFreshFirstShowGiftParameterModel alloc] init];
        parameter.tipType = 5;
        [FNFreshMrFreshService requestMrFreshFirstShowGiftWithParameter:parameter
                                                                success:nil
                                                                failure:nil];
        [self openPageWithURLString:linkUrl];
    }
}

- (FNFreshMrFreshEatInSuspensionView *)eatInSuspensionView {
    if (!_eatInSuspensionView) {
        CGFloat viewHeight = 160;
        BOOL shortBanenr = NO;
        if (FNFreshMrFreshSectionModel.shortBanner) {
            shortBanenr = YES;
        } else {
            shortBanenr = !(isHairIPhone);
        }
        viewHeight = shortBanenr ? round(200 * Ratio) : round(224 * Ratio);
        FNFreshMrFreshEatInSuspensionView *view = [FNFreshMrFreshEatInSuspensionView shareInstance];
        view.frame = CGRectMake(Main_Screen_Width - 100, viewHeight - 48, 200, 92);
        [self.view addSubview:view];
        view.hidden = YES;
        self.eatInSuspensionView = view;
    }
    return _eatInSuspensionView;
}

- (BOOL)isCurrentPage {
    // 添加延迟检查，避免tab切换时的状态竞态问题
    if (!_currentPage) {
        return NO;
    }

    // 检查是否是当前选中的tab
    FNFreshTabBarController *tabBarController = [FNFreshTabBarController shareInstance];
    if (tabBarController.selectedIndex != 0) { // 0是首页的index
        return NO;
    }

    // 检查navigationController状态，添加容错处理
    UINavigationController *navController = tabBarController.navigationController;
    if (!navController) {
        return _currentPage;
    }

    // 使用更可靠的方式检查presented状态
    BOOL hasPresentedVC = (navController.presentedViewController != nil);
    return _currentPage && !hasPresentedVC;
}

- (UIImageView *)freshSaleGuideTip {
    if (!_freshSaleGuideTip) {
        _freshSaleGuideTip = [[UIImageView alloc] initWithImage:[UIImage fnFresh_imageNamed:@"index_tips03"]];
        [self.view addSubview:_freshSaleGuideTip];
        [_freshSaleGuideTip setHidden:YES];
        [_freshSaleGuideTip mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(98, 83));
            make.right.equalTo(self.view.mas_right).offset(-15);
            make.bottom.equalTo(self.view.mas_bottom).offset(-(floorf((SCREEN_WIDTH - 18 - 24) / 3 + 69) + 48.f));
        }];
    }
    return _freshSaleGuideTip;
}

- (UIScrollView *)emptyScrollView {
    if (!_emptyScrollView) {
        _emptyScrollView = [[UIScrollView alloc] init];
        [self.view addSubview:_emptyScrollView];
        [_emptyScrollView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(self.view.mas_leading);
            make.top.equalTo(self.emptyNavBgView.mas_bottom);
            make.trailing.equalTo(self.view.mas_trailing);
            make.bottom.equalTo(self.view.mas_bottom);
        }];
    }
    return _emptyScrollView;
}

- (CIFilter *)grayFilter {
    if (!_grayFilter) {
        _grayFilter = [NSClassFromString(@"CAFilter") filterWithName:@"colorSaturate"];
        [_grayFilter setValue:@0 forKey:@"inputAmount"];
    }
    return _grayFilter;
}

- (FNFreshMrFreshLocationBottomView *)bottomAddressView {
    if (!_bottomAddressView) {
        WS(weakSelf)
        _bottomAddressView = [[FNFreshMrFreshLocationBottomView alloc] initWithHandler:^{
            [weakSelf presentPositioningViewController:nil];
            [FNFreshAgent eventWithTrackDataPrameters: @{
                @"page_col":@"200037",
                @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                @"track_type":@"2",
            }];
        }];
    }
    return _bottomAddressView;
}

- (FNFreshMrFreshLocationPermissionView *)locationPermissionView {
    if (!_locationPermissionView) {
        WS(weakSelf)
        _locationPermissionView = [FNFreshMrFreshLocationPermissionView instanceWithHandler:^(BOOL isPermission) {
//            [[FNCacheManager shareMananger] setCacheObject:@(YES) forKey:FNMrFreshOpenLocationPermissionShowed];
            [self writeToFile:@(YES) key:FNMrFreshOpenLocationPermissionShowed];
            if (isPermission) {
                [[FNFreshLocationLogic shareInstance] openPositionWithUrl:nil payloadUrl:nil];
            } else {
                [weakSelf presentPositioningViewController:nil];
            }
        }];
        [self.view addSubview:_locationPermissionView];
        [_locationPermissionView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.top.bottom.mas_equalTo(self.view);
        }];
        [self.view bringSubviewToFront:_locationPermissionView];
    }
    return _locationPermissionView;
}

- (FNFreshMrFreshCouponWillExpireTip *)couponExpireTipView {
    if (!_couponExpireTipView) {
        _couponExpireTipView =
        [[FNFreshBundleHandler fnFreshBundle] loadNibNamed:NSStringFromClass([FNFreshMrFreshCouponWillExpireTip class])
                                                     owner:self
                                                   options:nil].lastObject;

        [self.view addSubview:_couponExpireTipView];
        [_couponExpireTipView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.trailing.bottom.mas_equalTo(self.view);
            make.height.mas_equalTo(44);
        }];
    }
    return _couponExpireTipView;
}

#pragma mark - override

- (UIStatusBarStyle)preferredStatusBarStyle {
    if ([self.viewModel isResting]) {
        return UIStatusBarStyleDefault;
    }
    if (!self.abnormalNavigationBar.hidden || self.faultToleranceView) {
        return UIStatusBarStyleDefault;
    }
    return self.isBlackStateBar ? UIStatusBarStyleDefault : UIStatusBarStyleLightContent;
}

#pragma mark - FNFreshMrFreshFeedsGoodsGestureDelegate

- (void)pageViewControllerLeaveTop:(BOOL)isLeaveTop {
    self.cannotScroll = NO;
    if ([FNFreshMrFreshConstantCacheHandler shareInstance].isEnlargeMode) {
        self.collectionView.scrollEnabled = YES;
    }
}

- (void)pageViewControllerDidEndScroll:(BOOL)isEndScroll {
    if (!self.hangAdImgView.isHidden && isEndScroll) {
        [self.hangAdImgView showAdsImg:NO];
    }
}

#pragma mark - feeds流 scrollToTop

- (void)feedsFlowScrollToTop {
    // feeds流
    FNFreshMrFreshFeedsCollectionViewCell *cell =
    (FNFreshMrFreshFeedsCollectionViewCell *)[self.collectionView cellForItemAtIndexPath:self.feedsIndexPath];

    if (cell && [cell isKindOfClass:[FNFreshMrFreshFeedsCollectionViewCell class]]) {

        FNFreshMrFreshFeedsGoodsViewController *currentVC =
        (FNFreshMrFreshFeedsGoodsViewController *)cell.pageViewController.viewControllers.firstObject;
        [currentVC makePageViewControllerScrollToTop];
    }
}

///  Feeds流联动
- (void)feedsFlowScrolled:(UIScrollView *)scrollView andOffset:(CGFloat)YOffect {
    /// Feeds流数据安全性检查
    if (!self.feedsIndexPath || self.feedsIndexPath.section > self.collectionView.numberOfSections) {
        return;
    }
    
    /// 获取Feeds流所在的容器Cell
    FNFreshMrFreshFeedsCollectionViewCell *cell =
    (FNFreshMrFreshFeedsCollectionViewCell *)[self.collectionView cellForItemAtIndexPath:self.feedsIndexPath];
    if (cell && [cell isKindOfClass:[FNFreshMrFreshFeedsCollectionViewCell class]]) {

        FNFreshMrFreshFeedsGoodsViewController *currentVC =
        (FNFreshMrFreshFeedsGoodsViewController *)cell.pageViewController.viewControllers.firstObject;

        /// 设置反馈相关
        [currentVC setNegativeFeedbackDismiss];

        CGFloat bottomHeight = [FNFreshMrFreshConstantCacheHandler shareInstance].bottomHeight;
        /// Feeds流置顶时，底部滚动视图偏移的高度
        self.feedsCriticalPointOffsetY = self.collectionView.contentSize.height - (SCREEN_HEIGHT - bottomHeight);
        /// 当偏移量大于等于目标值的时候，底部滚动视图不在滚动，Feeds开始滚动
        if (ceilf(YOffect) >= ceilf(self.feedsCriticalPointOffsetY)) {
            self.cannotScroll = YES;
            /// 如果Feeds流上部内容过短，则在Feeds流置顶的时候显示小火箭
            if (self.contentIsMoreThanScreen == NO && self.isRocketShow == NO) {
                self.isRocketShow = YES;
                [[FNFreshTabBarController shareInstance] showRocketAnimated];
                [FNFreshAgent eventWithTrackDataPrameters:@{
                    @"page_col":@"127089",
                    @"page_id" :[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                    @"track_type":@"6",
                }];
            }
            if ([FNFreshMrFreshConstantCacheHandler shareInstance].isEnlargeMode) {
                scrollView.scrollEnabled = NO;
            }
            if (scrollView.contentOffset.y != self.feedsCriticalPointOffsetY) {
                scrollView.contentOffset = CGPointMake(0, self.feedsCriticalPointOffsetY);
            }
            FNFreshMrFreshFeedsGoodsViewController *currentVC =
            (FNFreshMrFreshFeedsGoodsViewController *)cell.pageViewController.viewControllers.firstObject;

            [currentVC makePageViewControllerScroll:YES];
            ///  Feeds流顶部Tab头
            if (self.fallFlowHeaderView && !self.fallFlowHeaderView.isConvert) {
                [self.fallFlowHeaderView showAnimating];
            }
        }
        /// Feeds不在滑动，底部滚动视图开始滑动
        else {
            if (self.cannotScroll) {
                if (scrollView.contentOffset.y != self.feedsCriticalPointOffsetY) {
                    scrollView.contentOffset = CGPointMake(0, self.feedsCriticalPointOffsetY);
                }
            } else {
                /// 如果Feeds流上部内容过短，则在Feeds流取消置顶的时候隐藏小火箭
                if (self.contentIsMoreThanScreen == NO && self.isRocketShow == YES) {
                    self.isRocketShow = NO;
                    [[FNFreshTabBarController shareInstance] showLogoElephantAnimated];
                }
                ///  Feeds流顶部Tab头
                if (self.fallFlowHeaderView && self.fallFlowHeaderView.isConvert) {
                    [self.fallFlowHeaderView endingAnimate];
                }
            }
        }
    }
}

#pragma mark - network request

- (void)requestHomeKeywords {
    FNFreshMrFreshHomeKeywordsParameter *para = [[FNFreshMrFreshHomeKeywordsParameter alloc] init];
    para.storeCode = [FNFreshUser shareInstance].shopId;
    para.queryKeyword = [self.viewModel getFirstKeywordInRecentSearch];
    WS(weakSelf)
    [FNFreshMrFreshService requestHomeKeywordsWithParameter:para success:^(FNFreshMrFreshHomeKeywordsResponse *responseObject, BOOL isCache) {
        [weakSelf.viewModel handleHomeKeywordsResponse:true keywordsResponse:responseObject];
    } failure:^(id responseObject, NSError *error) {
        [weakSelf.viewModel handleHomeKeywordsResponse:false keywordsResponse:nil];
    }];
}

- (void)requestMrFreshData {
    // v200 首页tabbar icon 2s后变化
    if (self.isFirstEntryShop) {
        // 每次切店第一次展示时更新
        self.startRequestHomeTimeInterval = [[NSDate date] timeIntervalSince1970];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            if (self.isCurrentPage) {
                [[FNFreshTabBarController shareInstance] checkHomeIcon];
            }
        });
    }
    self.contentIsMoreThanScreen = NO;
    self.hasLoadSecondScreen = NO;
    __block NSError *homeError = nil;
//    self.viewModel.responseModel = nil;
    self.viewModel.recommendResponseModel = nil;
    self.viewModel.eatInResponseModel = nil;
    self.storeListData = nil;
    self.hasEatIn = 0;
    self.hotStyleZoneIndex = 0;
    self.feedsIndexPath = nil;
    self.fallFlowHeaderView = nil;
    [self.bgImgCacheDic removeAllObjects];
    if (self.emptyScrollView) {
        [self.emptyScrollView removeFromSuperview];
        self.emptyScrollView = nil;
    }
    self.isNewOrMiniError = NO;
    [self.emptyNavBgView setHidden:YES];
    dispatch_group_enter(self.requestGroup);
    FNFreshMrFreshParameterModel *parameterModel = [[FNFreshMrFreshParameterModel alloc] init];
    parameterModel.storeCode = [FNFreshUser shareInstance].shopId;
    parameterModel.type = self.errorType;
    parameterModel.screenNum = 1;
    parameterModel.atmosphereH5ShowTime = [[self readFromFile:FNMrFreshAtmosphereH5ShowTime] doubleValue];
    parameterModel.latitude = [[FNFreshLocationLogic shareInstance].currentLocationInfo.latitude base64Decode];
    parameterModel.longitude = [[FNFreshLocationLogic shareInstance].currentLocationInfo.longitude base64Decode];
    parameterModel.queryKeyword = [self.viewModel getFirstKeywordInRecentSearch];
    parameterModel.latitudeStore = [FNFreshUser shareInstance].latitude;
    parameterModel.longitudeStore = [FNFreshUser shareInstance].longitude;
    
    __weak typeof(self)weakSelf = self;
    [FNFreshMrFreshService requestMrFreshDataWithParameter:parameterModel success:^(FNFreshMrFreshResponseModel *responseObject, BOOL isCache) {
        weakSelf.viewModel.responseModel = responseObject;
        dispatch_group_leave(weakSelf.requestGroup);
//        if (weakSelf.isCurrentPage) {
//            [[NSNotificationCenter defaultCenter] postNotificationName:FNFreshStoreServiceIconInfoNotification object:responseObject.iconInfo];
//        }
    } failure:^(FNFreshBaseResponseModel *responseObject, NSError *error) {
        homeError = error;
        dispatch_group_leave(weakSelf.requestGroup);
    }];
    if (isFNFreshTarget) {

        // 堂食
        dispatch_group_enter(self.requestGroup);
        FNFreshMrFreshEatInParameterModel *parameter = [[FNFreshMrFreshEatInParameterModel alloc] init];
        parameter.storeCode = [FNFreshUser shareInstance].shopId;
        parameter.latitude = [FNFreshUser shareInstance].latitude;
        parameter.longitude = [FNFreshUser shareInstance].longitude;
        [FNFreshMrFreshService requestEatInHomeWithParameter:parameter success:^(id responseObject, BOOL isCache) {
            weakSelf.viewModel.eatInResponseModel = responseObject;
            weakSelf.hasEatIn = ((FNFreshMrFreshEatInResponseModel *)responseObject).showEntrance;
            dispatch_group_leave(weakSelf.requestGroup);
        } failure:^(id responseObject, NSError *error) {
            dispatch_group_leave(weakSelf.requestGroup);
        }];
    }

    dispatch_group_notify(self.requestGroup, dispatch_get_main_queue(), ^{
        if (homeError) {
            [weakSelf stopProgress];
            [weakSelf.collectionView.mj_header endRefreshing];
            [weakSelf startProgressText:homeError.localizedDescription];
        } else {
            FNMrFreshPageFaultModel *pageFault = weakSelf.viewModel.responseModel.pageFault;
            if (pageFault) {
                FNFreshGPSAddressResponseModel *gpsModel =
                [[FNFreshGPSAddressResponseModel alloc] init];
                FNFreshLocationErrorInfoModel *errorInfo = [[FNFreshLocationErrorInfoModel alloc] init];
                errorInfo.errorUrl = pageFault.errorUrl;
                errorInfo.errorTip = pageFault.errorTip;
                errorInfo.topBackColor = pageFault.topBackColor;
                gpsModel.reservedStoreCode = pageFault.reservedStoreCode;
                gpsModel.errorInfo = errorInfo;
                [FNFreshUser shareInstance].pushH5LinkUrl = gpsModel.errorInfo.errorUrl;
                [FNFreshUser shareInstance].errorTip = gpsModel.errorInfo.errorTip;
                [FNFreshUser shareInstance].gpsAddressResponseModel = gpsModel;
                [self addFaultToleranceView];
            } else {
                [weakSelf.viewModel setupDataSource];
                // 请求门店切换弹框数据
                [weakSelf requestStoreList];
                [FNFreshUser shareInstance].isLogin?:[self.messageCountLab setHidden:YES];
                [FNFreshUser shareInstance].errorTip = nil; // 正常首页清除errorTip
            }
            weakSelf.showMsgCenter = weakSelf.viewModel.responseModel.showMsgCenter;
            DFileLog(@"home response atmosphereH5 = %@",weakSelf.viewModel.responseModel.atmosphereH5);
            [weakSelf setupTopBar];
            [weakSelf showEatInHomeEntrance];
            [weakSelf addRefreshGestures];
            [weakSelf.collectionView.mj_header endRefreshing];
            [weakSelf stopProgress];
            [weakSelf removeEmptyView];
            [weakSelf reloadCollectionViewData];
            //            [weakSelf reloadTableView];
            [weakSelf showAddressWarning];
            if (!weakSelf.isHandlePopwindow && weakSelf.isLeaveADPageVC) { // 只有是没弹过，且离开欢迎页进入首页才去请求popWindow

                //如果在容错页弹过窗 这里避免多余刷新
                if (weakSelf.isPopupWindowInFaulTolerance) {

                    //容错页弹过窗  直接标记为已经刷新过弹窗接口状态
                    weakSelf.handlePopwindow = YES;
                } else {
                    weakSelf.handlePopwindow = YES;
                    [weakSelf requestPopupWindowInfo];
                }
            }
            [weakSelf setNeedsStatusBarAppearanceUpdate];

            // 加载第二屏
            [weakSelf requestSecondScreen];
        }
    });
    [self requestMemCardTip];
    // 请求消息中心未读数
    ![FNFreshUser shareInstance].isLogin?:[self requestUnReadMessageCount];
    // 请求关键词
    [self requestHomeKeywords];
}

- (void)requestSecondScreen {

    FNFreshMrFreshParameterModel *parameterModel = [[FNFreshMrFreshParameterModel alloc] init];
    parameterModel.storeCode = [FNFreshUser shareInstance].shopId;
    parameterModel.type = self.errorType;
    parameterModel.screenNum = 2;
    parameterModel.moduleIdStr = self.viewModel.responseModel.moduleIdStr;
    parameterModel.isShowStoreService = [NSString stringWithFormat:@"%ld",self.viewModel.responseModel.isShowStoreService];
    parameterModel.latitudeStore = [FNFreshUser shareInstance].latitude;
    parameterModel.longitudeStore = [FNFreshUser shareInstance].longitude;
    
    __weak typeof(self)weakSelf = self;
    [FNFreshMrFreshService requestMrFreshSecondDataWithParameter:parameterModel success:^(FNFreshMrFreshResponseModel *responseObject, BOOL isCache) {

        [weakSelf.viewModel addNewData:responseObject];
        [weakSelf.collectionView reloadData];

        // 加载feeds流Tab数据
        [self requestFeedsTabData];

    } failure:^(FNFreshBaseResponseModel *responseObject, NSError *error) {
        self.collectionView.mj_footer = nil;
    }];
}

- (void)requestFeedsTabData {
    WS(weakSelf)
    [FNFreshMrFreshService requestFeedsTabWithStoreCode:[FNFreshUser shareInstance].shopId success:^(FNFreshMrFreshFeedsTabResponseModel *responseObject, BOOL isCache) {
        //        weakSelf.viewModel.feedsTabResponseModel = responseObject;
        [weakSelf.viewModel addFeedsGoodsData:responseObject];
        [weakSelf.collectionView reloadData];

        if (responseObject.tabList.count > 0) {
            [FNFreshTabBarController shareInstance].hasFeeds = YES;
            FNFreshMrFreshFeedsTabModel *feedsTab = responseObject.tabList.firstObject;
            NSString *title = feedsTab.title;
            if (title.length > 0) {
                if (title.length > 3) {
                    title = [title substringWithRange:NSMakeRange(0, 3)];
                }
                [[FNFreshTabBarController shareInstance] updateHomeFeedsTitle:title];
            } else {
                [[FNFreshTabBarController shareInstance] updateHomeFeedsTitle:@"精选"];
            }
        } else {
            [FNFreshTabBarController shareInstance].hasFeeds = NO;
        }
    } failure:^(id responseObject, NSError *error) {
    }];
}

- (void)requestMemCardTip {
    // 容错页不展示气泡提示
    if (self.faultToleranceView || self.isNewOrMiniError) {
        return;
    }

    NSString *storeCode = nil;
    storeCode = [FNFreshUser shareInstance].shopId;

    // 成功回调
    WS(weakSelf)
    void (^successBlock)(FNFreshMrFreshMemCardTipResponseModel *, BOOL) = ^(FNFreshMrFreshMemCardTipResponseModel *responseObject, BOOL isCache) {
        if (responseObject.voucherExpireBottomTips != nil && ![FNFreshUser shareInstance].faultTolerantStore) {
            weakSelf.isShowCouponExpireTip = YES;
            [weakSelf.couponExpireTipView setupWithData:responseObject.voucherExpireBottomTips handler:^(BOOL isCoupon) {
                if (isCoupon) {
                    // 跳转我的优惠券
                    FNFreshCouponViewController *vc = [[FNFreshCouponViewController alloc] initWithCouponClass:FNFreshCouponClassCoupon];
                    [[FNFreshTabBarController shareInstance] pushViewController:vc animated:YES];
                    [FNFreshAgent eventWithTrackDataPrameters: @{
                        @"page_col":@"189057",
                        @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                        @"track_type":@"2",
                    }];
                } else {
                    [FNFreshTabBarController shareInstance].lastVoucherExpireStoreCode = [FNFreshUser shareInstance].shopId;
                    [weakSelf.couponExpireTipView removeFromSuperview];
                    weakSelf.couponExpireTipView = nil;
                    weakSelf.isShowCouponExpireTip = NO;
                    [FNFreshAgent eventWithTrackDataPrameters: @{
                        @"page_col":@"189056",
                        @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                        @"track_type":@"2",
                    }];
                }
            }];
            [FNFreshAgent eventWithTrackDataPrameters: @{
                @"page_col":@"189055",
                @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                @"track_type":@"6",
            }];
        }
        if (!responseObject.voucherExpireBottomTips) {
            [weakSelf.couponExpireTipView removeFromSuperview];
            weakSelf.couponExpireTipView = nil;
            weakSelf.isShowCouponExpireTip = NO;
        }

        if(responseObject.orderDeliverModel != nil && ![self.orderIdSet containsObject:responseObject.orderDeliverModel.orderId]) {
            //订单状态提醒
            [weakSelf refreshOrderStateView:responseObject.orderDeliverModel isBottom:(![FNFreshUser shareInstance].faultTolerantStore && !weakSelf.isShowCouponExpireTip)];
        } else if (responseObject.tip.length > 0 && responseObject.memType == 1 && !weakSelf.isShowCouponExpireTip) { // 膨胀券提示信息
            [FNFreshTabBarController showTabbarRemindTipWithText:responseObject.tip tipType:7];
        } else if (responseObject.tip.length > 0 && responseObject.memType == 2 && !weakSelf.isShowCouponExpireTip) { // 新到券提示信息
            [FNFreshTabBarController showTabbarRemindTipWithText:responseObject.tip tipType:4];
        } else if (responseObject.tip.length > 0 && responseObject.memType == 3 && !weakSelf.isShowCouponExpireTip) { // 快过期券提示信息
            [FNFreshTabBarController showTabbarRemindTipWithText:responseObject.tip tipType:3];
        } else {
            [[FNFreshTabBarController shareInstance] hiddenTabbarTipReminderView];
            [self removeOrderStateView];
        }
        [weakSelf showQualification];
    };

    void (^failureBlock)(id, NSError *) = ^(id responseObject, NSError *error) {
    };
    FNFreshMrFreshMemCardTipParameterModel *params = [FNFreshMrFreshMemCardTipParameterModel new];
    params.storeCode = storeCode;
    params.lastStoreCode = [FNFreshTabBarController shareInstance].lastVoucherExpireStoreCode;
    [FNFreshMrFreshService requestMemCardBindTipWithStoreCode:params
                                                      success:successBlock
                                                      failure:failureBlock];
}

- (void)requestIsPractice {
    WS(weakSelf);
    [FNFreshMrFreshService requestIsPracticeWithSuccess:^(FNFreshIsPracticeResponseModel *responseObject, BOOL isCache) {
        weakSelf.viewModel.isPracticeResponseModel = responseObject;
        [FNFreshUser shareInstance].isPractice = responseObject.isPractice;
    } failure:^(id responseObject, NSError *error) {
        weakSelf.viewModel.isPracticeResponseModel = nil;
    }];
}

- (void)requestForSendGiftOnLoad {
    [self requestIsPractice]; // 登陆成功后判断是否是练习账号
    if ([FNFreshUser shareInstance].errorPracticeShopId != nil &&
        ![[FNFreshUser shareInstance].errorPracticeShopId isEqualToString:@""]) {
        NSDictionary *params = [NSDictionary dictionaryWithObjectsAndKeys:
                                [FNFreshUser shareInstance].errorPracticeShopId,@"storeCode", nil];
        [FNFreshMrFreshService requestMrFreshConditionalNewGiftWithParameter:params
                                                                     success:
         ^(id responseObject, BOOL isCache) {
            [self postRequestSendGiftNotification];
        } failure:^(id responseObject, NSError *error) {
            [self postRequestSendGiftNotification];
        }];
        return;
    } else {
        [self postRequestSendGiftNotification];
    }
    FNFreshMrFreshPopupWindowParameterModel *parameterModel = [[FNFreshMrFreshPopupWindowParameterModel alloc] init];
    parameterModel.storeCode = [FNFreshUser shareInstance].shopId ?: [FNFreshUser shareInstance].errorPracticeShopId;
    parameterModel.noviciateGiftBombTime = [[self readFromFile:FNMrFreshLastNewGuidancePopupWindowTime] doubleValue];
    parameterModel.commonCouponBombTime = [[self readFromFile:FNMrFreshLastCommonPopupWindowTime] doubleValue];
    parameterModel.commonCouponBombPeriods = [self readFromFile:FNMrFreshLastCommonPopupWindowSchedule];
    parameterModel.haveShowedCouponWindow = self.haveShowedCouponWindow;
    __weak typeof(self)weakSelf = self;
    [FNFreshMrFreshService requestForSendGiftOnLoadWithParameter:parameterModel success:^(FNFreshMrFreshSendGiftOnLoadResponseModel *responseObject, BOOL isCache) {

        /*主要牵涉到登录页dismiss动画和此接口返回的时间差
         1.如果接口在dismiss之前回调 对sendGiftURL进行赋值  dismiss完成立即执行openPage 进行页面跳转
         2.dismiss之后1s内回调 直接进行跳转
         3.dismiss之后1s后回调 不跳转
         */
        if ([weakSelf.sendGiftURL isEqualToString:@"stop"]) {
            //在登陆dismiss完之后1s后回来
            weakSelf.sendGiftURL = nil;
        } else if ([weakSelf.sendGiftURL isEqualToString:@"jump"]) {
            //在登陆dismiss完之后1s内回来
            weakSelf.sendGiftURL = nil;
            if (weakSelf.isCurrentPage) {

                [weakSelf openPageWithURLString:responseObject.targetUrl];
            }
        } else {
            //在登陆dismiss完之前回来
            weakSelf.sendGiftURL = responseObject.targetUrl;
        }
    } failure:nil];

    if (!isFNFreshTarget && self.isCurrentPage) {
        [self requestAcToGoVipProtocol];
    }
    //    [self reloadData];
}

/**
 API:sendandinfo接口回调后发送通知(kFNFreshRequestSendGiftNotification)
 */
- (void)postRequestSendGiftNotification {
    [[NSNotificationCenter defaultCenter] postNotificationName:kFNFreshRequestSendGiftNotification object:nil];
}

// 获取未读消息数
- (void)requestUnReadMessageCount {
    __weak typeof(self)weakSelf = self;
    [FNFreshCardService requestMessageCountWithParameter:nil success:^(FNFreshMessageResponseModel *responseObject, BOOL isCache) {
        if (responseObject.count > 0) {
            weakSelf.messageCountLab.hidden = NO;
            weakSelf.messageCountLab.text = [NSString stringWithFormat:@"%@",responseObject.count>99?@"99+":@(responseObject.count)];
            CGFloat multiple = 1 + (weakSelf.messageCountLab.text.length - 1) * 0.35;
            CGFloat width = 18 * multiple;
            weakSelf.messageCountWidthConstraint.constant = width;
        } else {
            weakSelf.messageCountLab.hidden = YES;
        }
    } failure:^(id responseObject, NSError *error) {
        weakSelf.messageCountLab.hidden = YES;
    }];
}

// 欧尚会员协议更新弹框提示
- (void)requestAcToGoVipProtocol {
    WS(weakSelf)
    [FNFreshMrFreshService requestActoGoVipProtocolWithSuccess:^(FNFreshMrFreshAcToGoVipProtocolResponse *responseObject, BOOL isCache) {
        if (responseObject.isPopup) {
            FNFreshMrFreshProtocolPopupViewController *viewController =
            [FNFreshMrFreshProtocolPopupViewController instanceWithDataModel:responseObject
                                                                     handler:^(BOOL dismiss, NSString *urlStr) {

                if (dismiss) {
                    FNSearchCommandCouponResponseModel *couponRechargeData = [FNFreshUtils shareInstance].couponRechargeModel;
                    if (couponRechargeData) {
                        [weakSelf rePopCoupon:couponRechargeData];
                    } else {
                        [weakSelf presentNewGuidanceViewController];
                    }

                } else {

                    UIViewController *h5 =
                    [[FNMediator sharedInstance] fnFreshH5Module_H5WebViewControllerWithUrl:urlStr];
                    [FNFreshTabBarController pushViewController:h5 animated:YES];

                }
            }];
            [weakSelf fn_presentViewController:viewController
                             customAnimateType:FNFreshMrFreshCustomAnimateTypeFromCenterKickBack
                                      viewSize:CGSizeMake(270, 295)
                                      duration:0.75
                                         alpha:0.5
                                        handle:nil];
        }

    } failure:^(id responseObject, NSError *error) {

    }];
}

// 补弹鲜口令
- (void)rePopCoupon:(FNSearchCommandCouponResponseModel *)couponRechargeResponseModel {
    FNFreshCouponRedeemedSuccessVC *vc =
    [[FNFreshCouponRedeemedSuccessVC alloc] initWithVoucherArrary:couponRechargeResponseModel.voucher
                                                           tipStr:couponRechargeResponseModel.noticeMsg
                                                        isVoucher:NO
                                                     jumpToCoupon:
     ^{
        UIViewController *viewController =
        [[FNMediator sharedInstance] freshCouponModel_FNFreshCouponViewController_initWithCouponClass:FNFreshCouponClassCoupon];
        [FNFreshTabBarController pushViewController:viewController animated:NO];

    }];

    [self fn_presentViewController:vc customAnimateType:FNFreshMrFreshCustomAnimateTypeFromCenterKickBack
                          viewSize:CGSizeMake(350*Ratio, 342*Ratio)
                          duration:0.75
                             alpha:0.5
                            handle:^{
        [FNFreshUtils shareInstance].couponRechargeModel = nil;
        [self presentNewGuidanceViewController];
    }];
}

/// 获取右上角切换门店入口的门店数据
- (void)requestStoreList {
    WS(weakSelf)
    FNFreshMrFreshStoreListParameter *parameterModel = [[FNFreshMrFreshStoreListParameter alloc] init];
    parameterModel.latitude = [FNFreshUser shareInstance].latitude;
    parameterModel.longitude = [FNFreshUser shareInstance].longitude;
    parameterModel.storeId = [FNFreshUser shareInstance].shopId;
    
    [FNFreshMrFreshService requestStoreListWithParameter:parameterModel success:
     ^(FNFreshMrFreshStoreListResponse *responseObject, BOOL isCache) {
        if (responseObject.stores.count > 1) {
            [weakSelf.changeStoreView setHidden:NO];
            weakSelf.storeListData = responseObject;
            [weakSelf showStoreListSwitchImgTips];
            [FNFreshAgent eventWithTrackDataPrameters:
             @{
                @"page_col":@"150036",
                @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
                @"track_type":@"6",
            }];
        } else {
            [weakSelf.changeStoreView setHidden:YES];
        }
    } failure:^(id responseObject, NSError *error) {
        [weakSelf.changeStoreView setHidden:YES];
    }];
}

/// 扫码
- (void)requestHomeScan:(NSString *)scanString {
    WS(weakSelf)
    __weak typeof(_scanVC) weakVC = _scanVC;
    [_scanVC startProgress];
    FNFreshMrFreshHomeScanParameter *para = [[FNFreshMrFreshHomeScanParameter alloc] init];
    para.storeId = [FNFreshUser shareInstance].shopId;
    para.scanString = scanString;
    [FNFreshMrFreshService requestHomeScanWithParamter:para success:
     ^( FNFreshMrFreshHomeScanResponse *responseObject, BOOL isCache) {
        [weakVC stopProgress];
        switch (responseObject.type) {
            case FNFreshMrFreshHomeScanTypeCMS:
            case FNFreshMrFreshHomeScanTypeGoods:
                [weakSelf scanOpenPageWithURLString:responseObject.scanString];
                break;

            case FNFreshMrFreshHomeScanTypeShoppingCard: {
                UIViewController *vc =
                [[FNMediator sharedInstance] bindMemberShipViewController_InitWithCardNum:
                 responseObject.scanString];
                [FNFreshTabBarController pushViewController:vc animated:YES];
            }
                break;
            case FNFreshMrFreshHomeScanTypeCoupon: {
                [weakSelf requestCouponsWithScanContent:responseObject.scanString];
            }
                break;

            default:
                break;
        }
    } failure:^(FNFreshMrFreshHomeScanResponse *responseObject, NSError *error) {
        [weakVC stopProgress];
        [weakVC startProgressText:responseObject.errorDesc];
        [weakSelf performSelector:@selector(runScan) withObject:nil afterDelay:2];
    }];
}

- (void)runScan {
    [_scanVC startScanRunning];
}

/// 扫码领券接口
- (void)requestCouponsWithScanContent:(NSString *)content {
    WS(weakSelf)
    __weak typeof(_scanVC)weakVC = _scanVC;
    [_scanVC startProgress];
    FNFreshCouponRechargeParameterModel *parameterModel = [[FNFreshCouponRechargeParameterModel alloc] init];
    BOOL phoneEmpty = [NSString isEmpty:[FNFreshUser shareInstance].phone];
    parameterModel.activity = phoneEmpty ? @"4" : @"1";
    parameterModel.phone = [FNFreshUser shareInstance].phone;
    parameterModel.voucherId = content;
    [FNFreshCardService requestWordCouponsWithParameter:parameterModel
                                                success:
     ^(FNFreshCouponRechargeResponseModel *responseModel, BOOL isCache) {

        [weakVC stopProgress];
        // 弹框
        [weakSelf presentScanCouponSuccessPopVcWithModel:responseModel];
    } failure:^(FNFreshBaseResponseModel *responseModel, NSError *error) {
        [weakVC stopProgress];
        [weakVC startProgressText:responseModel.errorDesc];
        [weakSelf performSelector:@selector(runScan) withObject:nil afterDelay:2];
    }];
}

- (void)requestArriveTime {
    CGPoint startPoint = [self.normalNavigationBar convertPoint:self.storeNameDynamicScrollView.frame.origin
                                                       fromView:self.locationView];
    CGPoint endPoint = self.messageView.frame.origin;
    if (self.storeListData.stores.count > 1) { // 切店按钮显示
        endPoint = self.changeStoreView.frame.origin;
    }
    self.storeNameDynamicScrollView.maxWidth = endPoint.x - startPoint.x - 16;
    self.storeNameDynamicScrollView.textDateArr = [[NSArray alloc] initWithObjects:self.viewModel.responseModel.storeName, nil];
    WS(weakSelf)
    [FNFreshMrFreshService requestStoreArriveTimeWithShopId:[FNFreshUser shareInstance].shopId success:
     ^(FNFreshMrFreshGetArriveTimeResponse *responseObject, BOOL isCache) {
        if (responseObject.arriveTime.length > 0) {
            CGPoint startPoint = [weakSelf.normalNavigationBar convertPoint:weakSelf.storeNameDynamicScrollView.frame.origin
                                                                   fromView:weakSelf.locationView];
            CGPoint endPoint = weakSelf.messageView.frame.origin;
            if (weakSelf.storeListData.stores.count > 1) { // 切店按钮显示
                endPoint = weakSelf.changeStoreView.frame.origin;
            }
            weakSelf.storeNameDynamicScrollView.maxWidth = endPoint.x - startPoint.x - 16;
            weakSelf.storeNameDynamicScrollView.textDateArr =
            [[NSArray alloc] initWithObjects:weakSelf.viewModel.responseModel.storeName,responseObject.arriveTime, nil];
        }
    } failure:nil];
}

#pragma mark - webview

static NSString *const JSMessageBridge = @"FNJSBridge";

- (WKWebView *)homeWebView {

    if (!_homeWebView) {
        WKWebViewConfiguration *config = [[WKWebViewConfiguration alloc] init];
        WKUserContentController *userController = [[WKUserContentController alloc] init];
        config.userContentController = userController;
        WKWebView *webView = [[WKWebView alloc] initWithFrame:[UIScreen mainScreen].bounds configuration:config];
        webView.userInteractionEnabled = NO;
        webView.opaque = NO;
//        _homeWebView.hidden = YES;
        webView.backgroundColor = [UIColor clearColor];
        webView.scrollView.contentInsetAdjustmentBehavior =
        UIScrollViewContentInsetAdjustmentNever;
//        // 注入js
//        NSString * consoleLog = @"console.log = (function(oriLogFunc){return function(str){oriLogFunc.call(console,str);window.webkit.messageHandlers.log.postMessage(str);}})(console.log);";
//        WKUserScript *userScriptLog = [[WKUserScript alloc] initWithSource:consoleLog injectionTime:WKUserScriptInjectionTimeAtDocumentStart forMainFrameOnly:YES];// forMainFrameOnly:NO(全局窗口)，yes（只限主窗口）
//        [webView.configuration.userContentController addUserScript:userScriptLog];
        [webView.configuration.userContentController addScriptMessageHandler:(id<WKScriptMessageHandler>)self.weakProxy name:JSMessageBridge];
//        [webView.configuration.userContentController addScriptMessageHandler:(id<WKScriptMessageHandler>)self.weakProxy name:@"log"];
        [[UIApplication sharedApplication].delegate.window addSubview:webView];
        _homeWebView = webView;
        DFileLog(@"window = %@, webView = %@",[UIApplication sharedApplication].delegate.window, webView);
    }

    return _homeWebView;
}

- (WKWebView *)getWebView {
    return _homeWebView;
}

/**
 * 释放资源
 */
- (void)releaseWebView {
    if (_homeWebView) {
        [self.homeWebView stopLoading];
        self.homeWebView.navigationDelegate = nil;
        self.homeWebView.UIDelegate = nil;
        [self.homeWebView.configuration.userContentController removeAllUserScripts];
        [self.homeWebView.configuration.userContentController removeScriptMessageHandlerForName:JSMessageBridge];
        [self.homeWebView removeFromSuperview];
        self.homeWebView = nil;
        if ([self.webViewAutoDismissTimer isValid]) {
            [self.webViewAutoDismissTimer invalidate];
            self.webViewAutoDismissTimer = nil;
        }
    }
    [self clearWebKitCache];
}

/// 清除cookie和localStorage
- (void)clearWebKitCache {
    WKWebsiteDataStore *dateStore = [WKWebsiteDataStore defaultDataStore];
    [dateStore fetchDataRecordsOfTypes:[WKWebsiteDataStore allWebsiteDataTypes]
                     completionHandler:^(NSArray<WKWebsiteDataRecord *> * __nonnull records) {
        for (WKWebsiteDataRecord *record  in records)
        {
            [[WKWebsiteDataStore defaultDataStore] removeDataOfTypes:record.dataTypes
                                                      forDataRecords:@[record]
                                                   completionHandler:^{
            }];
        }
    }];
}

///WKScriptMessageHandler
///JS交互
- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message {
    if ([message.name isEqualToString:@"log"]) {
        NSString *msg = message.body;
        NSLog(@"%@", msg);
    }

    // js交互
    if ([message.name isEqualToString:JSMessageBridge]) {

//        NSString *messageBody = message.body;
//        NSLog(@"message body = %@", messageBody);

        /**
         传过来的是jsonString
         */
        if (![message.body isKindOfClass:[NSString class]]) {
            DFileLog(@"JS message body 格式不对");
            return;
        }

        // 解析
        NSData *data = [message.body dataUsingEncoding:NSUTF8StringEncoding];
        NSError *error;
        NSDictionary *dictionary = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:&error];
//        // 获取当前时间
//        NSDate *currentDate = [NSDate date];
//
//        // 创建并配置日期格式化器
//        NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
//        [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss.SSS"]; // SSS 表示毫秒
//
//        // 将日期转换为字符串并打印
//        NSString *formattedDate = [dateFormatter stringFromDate:currentDate];
//        NSLog(@"message 消息时间： %@, mesage body = %@", formattedDate, messageBody);
        
        NSString *methodName = [dictionary safeObjectForKey:@"methodName"];
        DFileLog(@"web js methodName = %@",methodName);
        if ([methodName isEqualToString:@"indexAnimationStart"]) {
            DFileLog(@"currentPage = %d",_currentPage?1:0);
            if (_currentPage) {
                // 开始显示
                _homeWebView.hidden = NO;
                DFileLog(@"webView-indexAnimationStart 开始显示");
                // save the atmosphereH5ShowTime
                if (self.viewModel.responseModel.atmosphereH5ShowTime > 0) {

                    [self writeToFile:@(self.viewModel.responseModel.atmosphereH5ShowTime)
                                  key:FNMrFreshAtmosphereH5ShowTime];
                }
            }

        } else if ([methodName isEqualToString:@"indexAnimationEnd"]) {

            _homeWebView.hidden = YES;
            DFileLog(@"webView-indexAnimationEnd 隐藏");
            // 先隐藏再释放
            [self releaseWebView];
        }
    }
}

- (FNH5WeakProxy *)weakProxy {
    if (!_weakProxy) {
        _weakProxy = [FNH5WeakProxy proxyWithTarget:self];
    }
    return _weakProxy;
}

@end
