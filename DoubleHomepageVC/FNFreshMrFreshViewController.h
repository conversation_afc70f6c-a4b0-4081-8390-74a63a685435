//
//  FNFreshMrFreshViewController.h
//  FNFresh
//
//  Created by yong<PERSON><PERSON> on 2017/2/27.
//  Copyright © 2017年 FeiNiu. All rights reserved.
//

#import "FNFreshBaseViewController.h"
#import "FNHomeSearchButton.h"

extern NSNotificationName const kFreshLocationNotification;
extern NSString *const FNFreshMrFreshNativePracticeErrorTips;
extern NSString *const FNFreshMrFreshNativeMiniAndCloseErrorTips;
extern NSString *const FNMrFreshKeywordsChangeNotificationName;

@class FNFreshMrFreshViewModel, FNFreshMrFreshFaultToleranceView, FNFreshMrFreshPopupWindowResponseModel, FNFreshMrFreshTooltipView, FNFreshMrFreshBaseCollectionView, FNFreshMrFreshWaterfallFlowHeadReusableView, FNMrFreshGoodsModel, FNFreshMrFreshGradientView,WKWebView;

@interface FNFreshMrFreshViewController : FNFreshBaseViewController

@property (weak, nonatomic) IBOutlet FNFreshMrFreshGradientView *emptyNavBgView;
@property (weak, nonatomic) IBOutlet FNFreshMrFreshBaseCollectionView *collectionView;
@property (weak, nonatomic) IBOutlet UIView *configBgView; // 默认背景
@property (strong, nonatomic) UIImageView *bannerBgImgView;
@property (weak, nonatomic) IBOutlet FNHomeSearchButton *searchBtn; // 搜索按钮
@property (weak, nonatomic) IBOutlet UIImageView *switchStoreListImg;
@property (assign, nonatomic, getter=isCurrentPage) BOOL currentPage;  //当前页面
@property (assign, nonatomic) BOOL isNormalBgNavi;
@property (assign, nonatomic, getter=isVersionUpdateFinish) BOOL versionUpdateFinish;    //版本更新结束
@property (strong, nonatomic) FNFreshMrFreshViewModel *viewModel;
@property (weak, nonatomic) FNFreshMrFreshFaultToleranceView *faultToleranceView;
@property (assign, nonatomic, getter=isHandlePopwindow) BOOL handlePopwindow; //启动后是否请求过弹窗接口
@property (assign, nonatomic, getter=isFirstEntryShop) BOOL firstEntryShop;  //第一次进入当前门店
@property (copy, nonatomic) NSString *sendGiftURL;
@property (strong, nonatomic) FNFreshMrFreshTooltipView *addressTooltipView;
@property (strong, nonatomic) NSIndexPath *saleIndexPath;   // 鲜特卖Cell的位置
@property (assign, nonatomic) NSInteger haveShowedCouponWindow;
@property (assign, nonatomic, getter=isPopupWindowInFaulTolerance) BOOL popupWindowInFaulTolerance;  //是否在容错页弹过礼包
@property (strong, nonatomic) FNFreshMrFreshWaterfallFlowHeadReusableView *fallFlowHeaderView;
@property (assign, nonatomic, getter=isShowSaleGuideTip) BOOL showSaleGuideTip;
@property (assign, nonatomic) NSInteger hotStyleZoneIndex;    // 爆款专区section位置
@property (strong, nonatomic) NSIndexPath *nxyIndexPath; // 新人N选1cell位置
@property (strong, nonatomic) NSIndexPath *feedsIndexPath; // feeds流cell位置
@property (strong, nonatomic) NSIndexPath *oldNXOneIndexPath; // 老人n选1 cell位置

@property (assign, nonatomic) BOOL isNewOrMiniError;
@property (strong, nonatomic) NSMutableDictionary <NSString *, UIImage *> *bgImgCacheDic; //key: colorStr
@property (assign, nonatomic) BOOL isShowCouponExpireTip; // 首页是否展示优惠券过期提示


- (BOOL)isCurrentPage;
- (void)scrollToTop;
- (void)scrollToFeeds;
- (void)showAddressWarning;
- (void)setOutOfScopeErrorWithAddress:(NSString *)address isMini:(BOOL)isMini;
- (void)openPageWithURLString:(NSString *)URLString;
- (void)showSaleNewGuideView:(CGFloat)scrollViewOffSetY;
- (void)setShopCartWithImageView:(UIImageView *)imgView andGoodsModel:(FNMrFreshGoodsModel *)goodsModel source:(NSString *)source;
- (void)saleGuideTipDisappear;
- (WKWebView *)getWebView;
/**
 给首页覆盖一个H5试图，暂时用于欧尚合并优鲜的H5公告页
 */
- (void)addH5WebViewWithLinkUrl:(NSString *)linkUrl;
- (void)showStoreListSwitchImgTips;

- (void)reloadOldNXOneCell;

// 处理切店
- (void)handleSwitchStoreList;

@end
