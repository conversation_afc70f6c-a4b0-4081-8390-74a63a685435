//
//  FNFreshMrFreshFeedsGoodsCollectionFlowLayout.m
//  FNFresh
//
//  Created by wang<PERSON> on 2021/8/4.
//  Copyright © 2021 FeiNiu. All rights reserved.
//

#import "FNFreshMrFreshFeedsGoodsCollectionFlowLayout.h"

@interface FNFreshMrFreshFeedsGoodsCollectionFlowLayout ()

@property (strong, nonatomic) NSMutableArray <UICollectionViewLayoutAttributes *>*itemAttributesArray;

@property (weak, nonatomic) id<UICollectionViewDelegateFlowLayout> delegate;

@end

@implementation FNFreshMrFreshFeedsGoodsCollectionFlowLayout

- (void)prepareLayout {
    [super prepareLayout];
    self.minimumLineSpacing = 9;
    self.minimumInteritemSpacing = 9;
    self.sectionInset = UIEdgeInsetsMake(6, 12, 9, 12);
    [self calculateItemAttributes];
}

- (CGSize)collectionViewContentSize {
    UICollectionViewLayoutAttributes *subAttr =
    [self.itemAttributesArray safeObjectAtIndex:self.itemAttributesArray.count - 2];
    
    if (subAttr) {
        CGFloat maxY = MAX(CGRectGetMaxY(subAttr.frame),
                           CGRectGetMaxY(self.itemAttributesArray.lastObject.frame));
        
        CGSize contentSize = CGSizeMake(CGRectGetWidth(self.collectionView.bounds),
                                        maxY + self.sectionInset.bottom);
        return contentSize;
    }
    CGSize contentSize =
    CGSizeMake(CGRectGetWidth(self.collectionView.bounds),
               CGRectGetMaxY(self.itemAttributesArray.lastObject.frame) + self.sectionInset.bottom);
    
    return contentSize;
}

- (void)invalidateLayout {
    
    [super invalidateLayout];
    [self.itemAttributesArray removeAllObjects];
}

- (UICollectionViewLayoutAttributes *)layoutAttributesForItemAtIndexPath:(NSIndexPath *)indexPath {
    
    return self.itemAttributesArray[indexPath.row];
}

- (NSArray<UICollectionViewLayoutAttributes *> *)layoutAttributesForElementsInRect:(CGRect)rect {
    
    NSMutableArray *array = [NSMutableArray array];
    for (UICollectionViewLayoutAttributes *attributes in self.itemAttributesArray) {
        
        if (CGRectIntersectsRect(rect, attributes.frame)) {
            
            [array addObject:attributes];
        }
    }
    return array;
}

- (BOOL)shouldInvalidateLayoutForBoundsChange:(CGRect)newBounds {
    
    return NO;
}

- (void)calculateItemAttributes {
    NSInteger numberOfItem = [self.collectionView numberOfItemsInSection:0];
    CGFloat leftOriginY = self.sectionInset.top;
    CGFloat rightOriginY = self.sectionInset.top;
    CGFloat leftX = self.sectionInset.left;
    CGFloat itemWidth = (SCREEN_WIDTH - 33) / 2;
    CGFloat rightX = CGRectGetWidth(self.collectionView.bounds) - self.sectionInset.right - itemWidth;
    for (NSInteger i = 0; i < numberOfItem; i ++) {
        
        CGSize itemSize = CGSizeZero;
        
        if ([self.delegate respondsToSelector:@selector(collectionView:layout:sizeForItemAtIndexPath:)]) {
            
            NSIndexPath *itemIndexPath = [NSIndexPath indexPathForRow:i inSection:0];
            itemSize = [self.delegate collectionView:self.collectionView
                                              layout:self
                              sizeForItemAtIndexPath:itemIndexPath];
            
        }
        CGFloat originX = 0;
        CGFloat originY = 0;
        if (leftOriginY > rightOriginY) {
            originX = rightX;
            originY = rightOriginY;
        } else {
            originX = leftX;
            originY = leftOriginY;
        }
        CGRect frame = (CGRect){{originX, originY}, itemSize};
        UICollectionViewLayoutAttributes *attributes =
        [UICollectionViewLayoutAttributes layoutAttributesForCellWithIndexPath:[NSIndexPath indexPathForRow:i inSection:0]];
        attributes.frame = frame;
        if (self.itemAttributesArray.count > i) {
            
            [self.itemAttributesArray replaceObjectAtIndex:i withObject:attributes];
        } else {
            
            [self.itemAttributesArray addObject:attributes];
        }
        if (leftOriginY > rightOriginY) {
            rightOriginY += self.minimumLineSpacing + itemSize.height;
        } else {
            leftOriginY += self.minimumLineSpacing + itemSize.height;
        }
    }
}

- (NSMutableArray *)itemAttributesArray {
    
    if (!_itemAttributesArray) {
        
        _itemAttributesArray = [NSMutableArray array];
    }
    return _itemAttributesArray;
}

- (id<UICollectionViewDelegateFlowLayout>)delegate {
    
    return (id<UICollectionViewDelegateFlowLayout>)self.collectionView.delegate;
}

@end
