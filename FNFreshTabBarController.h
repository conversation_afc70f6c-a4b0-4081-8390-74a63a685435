//
//  FNFreshTabBarController.h
//  FNFresh
//
//  Created by DC on 2017/2/8.
//  Copyright © 2017年 FeiNiu. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "FNFreshBaseTabBarViewController.h"
#import "FNFreshPasteboardRechargeHandler.h"

@class FNFreshMrFreshViewController;

typedef NS_ENUM(NSUInteger, FNTabBarViewControllerType) {
    FNTabBarViewControllerTypeHome = 0,         // 首页
    FNTabBarViewControllerTypeDepartments,      // 分类
    FNTabBarViewControllerTypeSpecialSell,      // 常买清单  // 153版本后表示线下门店首页
    FNTabBarViewControllerTypeShoppingCart,     // 购物车
    FNTabBarViewControllerTypeMyAccount         // 我的账户
};

@interface FNFreshTabBarController : FNFreshBaseTabBarViewController

/**
 是否已经展示优鲜维护页面
 */
@property (nonatomic, assign, getter=isShowMaintenanceVc) BOOL showMaintenanceController;

@property (nonatomic, strong) UIImageView *shopCartTabIcon;
@property (nonatomic, strong) UILabel *badgeLabel;

@property (nonatomic, assign) CGPoint shopCartTabIconCenter;
@property (nonatomic, copy) NSString *lastVoucherExpireStoreCode; // 上一次展示优惠券过期底栏的门店
//1：新人礼包到账提醒，2：绑定会员卡提醒，3：优惠券过期提醒，4：新到券提醒，6：集点换购，7：鲜任务
@property (nonatomic, assign) NSInteger tipType;
@property (nonatomic, assign) BOOL tipShow; // 底部导航提醒是否出现
@property (nonatomic, strong, readonly) FNFreshMrFreshViewController *homeVC;

@property (nonatomic, assign, readonly) CGFloat tabBarHeight;

@property (nonatomic, strong) FNFreshPasteboardRechargeHandler *pasteboardHandler;
@property (nonatomic, assign) BOOL hasFeeds;

+ (instancetype)shareInstance;

+ (void)pushViewController:(UIViewController *)viewController animated:(BOOL)animated;

- (void)switchTabBarWithParam:(NSDictionary*)param;

- (void)hiddenTabbarTipReminderView;

/** 双首页相关接口  */
- (void)hideTabbar:(BOOL)hide;

/** 返回并展示对应的Controller */
- (void)backToTagetViewControllerWithType:(FNTabBarViewControllerType)type
                                 animated:(BOOL)animated;

/**返回到FNFreshTabBarController*/
- (void)backToFNFreshTabBarControllerAnimated:(BOOL)animated;

/** 获取TabBar上对应Controller */
- (UIViewController *)viewControllerInTabBarWithType:(FNTabBarViewControllerType)type;
/** 更新购物车总数量 */
-(void)updateBadgeFinal:(NSString *)finalCount;
/** 购物车总数量置成0 */
-(void)updateBadge2Zero;
/** 获取购物车总数量 */
- (NSString *)getBadge;
/** 更新堂食购物车总数量 */
-(void)updateEatInStoreBadgeFinal:(NSString *)finalCount;
/** 获取堂食购物车总数量 */
- (NSString *)getEatInStoreBadge;

+ (void)showTabbarRemindTipWithText:(NSString *)text tipType:(NSInteger)tipType;

/// v152
/// 首页手动选择展示线下门店
- (void)switchToOfflineHomeByManual;
/// 手动q选择展示新店首页
- (void)switchToNewStoreHomeByManual;
/// 首页手动选择展示mini门店
- (void)switchToMiniHome;
/// 展示小火箭动画，仅大润发优鲜触发该动画
- (void)showRocketAnimated;
/// 小火箭转大象动画
- (void)showLogoElephantAnimated;
- (void)dismissElephantLogoAnimated;
- (void)selectHomeAnimated;
- (void)checkHomeIcon;
- (void)updateHomeFeedsTitle:(NSString *)title;
- (BOOL)isShowRocket;

/** v165- 线下切线上逻辑完善，当定位到线下时，手动调backtoHomepage接口，查看线上门店状态：
    例如：定位到线下募卡圈或者新店，线上是1小时达开业状态*/
- (void)offlineToOnline;
/// 卫星店/仓
- (void)chooseWXStore;

@end
