//
//  AppDelegate.h
//  FNFresh
//
//  Created by DC on 2017/2/7.
//  Copyright © 2017年 FeiNiu. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "FNAppDelegateService.h"
#import <UserNotifications/UserNotifications.h>

// release_203
@interface AppDelegate : UIResponder <UIApplicationDelegate, UNUserNotificationCenterDelegate>

@property (strong, nonatomic) FNAppDelegateService *appDelegateService;

@property (nonatomic, strong) FNFreshADPageViewController *defaultVC;

@property (nonatomic, assign) BOOL isOpenWithNotification;

@property (strong, nonatomic) UIWindow *window;

/** 用于标志推送push时间 */
@property (nonatomic, strong) NSDate *notificationPushTimeMark;

@end

