//
//  FNFreshTabBarController.m
//  FNFresh
//
//  Created by DC on 2017/2/8.
//  Copyright © 2017年 FeiNiu. All rights reserved.
//

#import "FNFreshTabBarController.h"
#import "FNBaseNavigationController.h"
#import "FNFreshMrFreshViewController.h"
#import "FNFreshDepartmentContainerController.h"
#import "FNFreshUserCenterViewController.h"
#import "FNMediator+FNFreshShopCartModule.h"
#import "FNFreshStoreListViewController.h"
#import "FNMediator+FreshMrFreshModule.h"
#import "UITabBar+Badge.h"
#import "FNFreshMrFreshCouponExpirationReminderView.h"
#import "FNFreshFirstShowGiftParameterModel.h"
#import "FNFreshFirstShowGiftResponseModel.h"
#import "FNFreshMrFreshConstantCacheHandler.h"
#import "FNFreshMrFreshViewController+OrderState.h"
#import "FNFreshOfflineBaseViewController.h"
#import "FNFreshMrFreshSwitchLocationParameterModel.h"
#import "FNFreshGPSAddressResponseModel.h"
#import "FNFreshCustomHomeTabBarItemView.h"
#import "FNFreshStoreServiceNotification.h"
#import "FNShareImageCompressor.h"
#import "FNFreshTarget_H5Module.h"
#import "FNFreshNewStoreHomeInfoResponseModel.h"
#import "FNFreshMrFreshService.h"
#import "FNFreshShopCartContainerController.h"
#import "FNFreshTabBarViewModel.h"

extern NSString * const FNMrFreshTaskBottomTipShowed;
extern NSString * const kFNFreshScrollToTopNotification;
extern NSString * const kFNFreshOffToOnlineNotification;

@interface FNFreshTabBarController () <UITabBarControllerDelegate>

@property (nonatomic, copy) NSString *totalCount;
@property (nonatomic, copy) NSString *eatInStoretotalCount;
@property (nonatomic, weak) UIView *tabbarTipRemindView;
@property (nonatomic, strong) FNFreshMrFreshViewController *homeVC;
@property (nonatomic, strong) FNFreshDepartmentContainerController *categaryVC;
@property (nonatomic, strong) FNFreshOfflineBaseViewController *offlineVC;
@property (nonatomic, strong) NSMutableArray *tabbarButtonArr;
@property (nonatomic, strong) UIImageView *homeTabbarTopImgV;
@property (nonatomic, strong) FNFreshCustomHomeTabBarItemView *homeTabBarItemView;

@property (nonatomic, strong) FNFreshTabBarViewModel *viewModel;
@property (nonatomic, strong) UIImageView *tabBgImageView;

/// 标记第一次页面展示
@property (nonatomic, assign) BOOL viewIsAppearFirstTime;

@end

@implementation FNFreshTabBarController

+ (instancetype)shareInstance {
    static FNFreshTabBarController *_shareInstance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _shareInstance = [[FNFreshTabBarController alloc] init];
    });
    return _shareInstance;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self addChildView];
    [self createControllers];
    [self.viewModel requestTabBottomInfo];
    self.delegate = self;
    
    self.viewIsAppearFirstTime = YES;
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshCount) name:kFreshLocationNotification object:nil];
    
    //线下切线上，如果发生门店id变化，接收通知，调用线下返回线上的接口/location/backToHomePage
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(offlineToOnline) name:kFNFreshOffToOnlineNotification object:nil];
    
    //189监听卫星仓通知 193与pd确认去掉该逻辑
    //    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(actionIconInfoNotification:) name:FNFreshStoreServiceIconInfoNotification object:nil];
    //监听门店切换
    [NSNotificationCenter.defaultCenter addObserver:self selector:@selector(shopDidChanged) name:kFNFreshAddressChangedNotification object:nil];
}

- (void)addChildView {
    [self.tabBar insertSubview:self.tabBgImageView atIndex:0];
    self.tabBgImageView.frame = self.tabBar.frame;
    
    [self.tabBar addSubview:self.homeTabBarItemView];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    // 初始化App时不请求购物车数量，首次启动延迟到首页DidAppear后(V2.0.8)
    if (self.viewIsAppearFirstTime) {
        return;
    }
    if ([FNFreshUser shareInstance].shopId.length > 0) {
        [[FNMediator sharedInstance] fnFreshShopCartService_RequestGetShopCartMerchandiseTotalNumber];
    }
    if (isFNFreshTarget && self.homeVC.hasEatIn) {
        [[FNMediator sharedInstance] fnFreshShopCartService_RequestGetShopCartMerchandiseAEatInStoreTotalNumber];
    }
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    
    self.viewIsAppearFirstTime = NO;
    
    //初始化购物车TabBarIcon位置信息
    [self initializeShopCartTabIcon];
    if ([FNFreshUser shareInstance].locationFinished) {
        [FNFreshAgent eventWithTrackDataPrameters:@{
            @"page_col":@"195042",
            @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
            @"track_type":@"6",
        }];
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    
    [super viewWillDisappear:animated];
    //    [self hiddenTabbarTipReminderView];
}

- (void)viewDidLayoutSubviews {
    [super viewDidLayoutSubviews];
    if (self.tabbarTipRemindView) {
        [self.view bringSubviewToFront:self.tabbarTipRemindView];
    }
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
}

- (CGFloat)tabBarHeight {
    return CGRectGetHeight(self.tabBar.bounds);
}

- (void)refreshCount {
    [[FNMediator sharedInstance] fnFreshShopCartService_RequestGetShopCartMerchandiseTotalNumber];
}

- (NSMutableArray *)tabbarButtonArr {
    if (!_tabbarButtonArr) {
        _tabbarButtonArr = [NSMutableArray array];
    }
    return _tabbarButtonArr;
}

- (UIImageView *)homeTabbarTopImgV {
    if (!_homeTabbarTopImgV) {
        _homeTabbarTopImgV = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"index_bg_semicircle"]];
        _homeTabbarTopImgV.contentMode = UIViewContentModeScaleToFill;
        _homeTabbarTopImgV.backgroundColor = [UIColor redColor];
    }
    return _homeTabbarTopImgV;
}

- (FNFreshCustomHomeTabBarItemView *)homeTabBarItemView {
    if (!_homeTabBarItemView) {
        _homeTabBarItemView = [[FNFreshCustomHomeTabBarItemView alloc] initWithFrame:CGRectZero];
    }
    return _homeTabBarItemView;
}

- (FNFreshTabBarViewModel *)viewModel {
    if (!_viewModel) {
        _viewModel = [[FNFreshTabBarViewModel alloc] init];
        WS(weakSelf);
        _viewModel.triggerTabBarUpdate = ^(FNFreshTabBarUpdateType type) {
            [weakSelf handleUpdateTabBar:type];
        };
    }
    return _viewModel;
}

- (UIImageView *)tabBgImageView {
    if (!_tabBgImageView) {
        _tabBgImageView = [[UIImageView alloc] init];
        _tabBgImageView.hidden = true;
        _tabBgImageView.clipsToBounds = true;
        _tabBgImageView.contentMode = UIViewContentModeTop;
        _tabBgImageView.userInteractionEnabled = true;
    }
    return _tabBgImageView;
}

#pragma mark - 底部导航tabBar选择动画

- (void)showRocketAnimated {
    [self.homeTabBarItemView showRocketAnimation];
}

- (void)showLogoElephantAnimated {
    [self.homeTabBarItemView showElephantLogoAnimation:NO];
}

- (BOOL)isShowRocket {
    return self.homeTabBarItemView.isShowRocketImg;
}

- (void)selectHomeAnimated {
    [self.homeTabBarItemView selectedHomeAnimation];
}

- (void)dismissElephantLogoAnimated {
    [self.homeTabBarItemView disSelectedHomeAnimation];
}

- (void)checkHomeIcon {
    [self performSelector:@selector(checkHomeShow) withObject:nil afterDelay:0.1];
}

- (void)checkHomeShow {
    [self.homeTabBarItemView checkHomeShow];
}

- (void)updateHomeFeedsTitle:(NSString *)title {
    [self.homeTabBarItemView updateFeedsTitle:title];
}

- (void)setHasFeeds:(BOOL)hasFeeds {
    _hasFeeds = hasFeeds;
    [self.homeTabBarItemView setHasFeeds:hasFeeds isRightAtHome:self.selectedIndex == 0];
}

#pragma mark - 初始化购物车TabBarIcon位置信息

/**
 初始化购物车TabBarIcon位置信息
 */
- (void)initializeShopCartTabIcon {
    NSMutableArray *tabBarBtnArray = [NSMutableArray array];
    [self.tabbarButtonArr removeAllObjects];
    for (UIView *subView in self.tabBar.subviews) {
        if ([subView isKindOfClass:NSClassFromString(@"UITabBarButton")]) {
            [tabBarBtnArray addObject:subView];
        }
    }
    if (tabBarBtnArray.count <= 0) {
        return;
    }
    NSInteger index = 2;
    if (tabBarBtnArray.count == 5) {
        index = 3;
    }
    // 遍历tabBarBtnArray数组按照x坐标依次排序
    [self sortByBtnOriginX:tabBarBtnArray];
    [self.tabbarButtonArr addObjectsFromArray:tabBarBtnArray];
    //找到常卖清单的tabBarButton
    UIView *oftenBuyTabBarBtn = [tabBarBtnArray safeObjectAtIndex:index];
    for (UIView *subView in oftenBuyTabBarBtn.subviews) {
        if ([subView isKindOfClass:[UIImageView class]]) {
            self.shopCartTabIcon = (id)subView;
            self.shopCartTabIconCenter = [oftenBuyTabBarBtn convertPoint:subView.center toView:self.tabBar];
        }
    }
    if (isFNFreshTarget) {
        // 首页icon添加圆弧顶
//        [self.tabBar addSubview:self.homeTabBarItemView];
        [self.tabBar bringSubviewToFront:self.homeTabBarItemView];
        UIView *homeTabbarBtn = self.tabbarButtonArr.firstObject;
        //        self.homeTabBarItemView.frame = CGRectMake((homeTabbarBtn.bounds.size.width - 64)/2.f + homeTabbarBtn.frame.origin.x, -13, 64, 45);
        self.homeTabBarItemView.frame = homeTabbarBtn.frame;
    }
}

- (void)sortByBtnOriginX:(NSMutableArray *)btnArr {
    NSComparator cmptor = ^(UIView *obj1, UIView *obj2){
        if (obj1.frame.origin.x < obj2.frame.origin.x) {
            return (NSComparisonResult)NSOrderedAscending;
        }
        if (obj1.frame.origin.x > obj2.frame.origin.x) {
            return (NSComparisonResult)NSOrderedDescending;
        }
        return (NSComparisonResult)NSOrderedSame;
    };
    [btnArr sortUsingComparator:cmptor];
}

- (void)createControllers {
    self.homeVC = (FNFreshMrFreshViewController *)[[FNMediator sharedInstance] freshMrFreshModule_FNFreshMrFreshViewController_Init];
    
    self.categaryVC = [[FNFreshDepartmentContainerController alloc]
                       initWithPresetFirstCategoryRes:nil
                       fromType:FNDepartmentSourceTypeTabBar
                       extraCategoryList:nil];
    
    NSString *selectedHomeImgName = isFNFreshTarget ? @"icon_home_default_s" : @"icon_home_s";
    [self FNAddChildViewController:self.homeVC title:@"首页"
                             image:[UIImage fnFresh_imageNamed:@"icon_home_n"]
                     selectedImage:[UIImage fnFresh_imageNamed:selectedHomeImgName]];
    
    [self FNAddChildViewController:self.categaryVC
                             title:@"分类"
                             image:[UIImage fnFresh_imageNamed:@"icon_classification_n"]
                     selectedImage:[UIImage fnFresh_imageNamed:@"icon_classification_s"]];
    
    //TODO:
    //    UIViewController *viewController = [[FNMediator sharedInstance] freshListModule_specialSellProductListViewControllerInitialize];
    //    FNBaseNavigationController *navigationController = [[FNBaseNavigationController alloc] initWithRootViewController:viewController];
    
    // 中间item 线下首页 v152
    self.offlineVC = [FNFreshOfflineBaseViewController shareInstance];
    [self FNAddChildViewController:self.offlineVC title:@"会员中心" 
                             image:[UIImage imageNamed:@"icon_offline_home_n"]
                     selectedImage:[UIImage imageNamed:@"icon_offline_home_s"]];
    
    FNFreshShopCartContainerController *shopCartVC = [[FNFreshShopCartContainerController alloc] init];
    [self FNAddChildViewController:shopCartVC 
                             title:@"购物车"
                             image:[UIImage fnFresh_imageNamed:@"icon_shoppingcat_n"]
                     selectedImage:[UIImage fnFresh_imageNamed:@"icon_shoppingcat_s"]];
    
    [self FNAddChildViewController:[FNFreshUserCenterViewController shareInstance] 
                             title:@"我的"
                             image:[UIImage fnFresh_imageNamed:@"icon_my_n"]
                     selectedImage:[UIImage fnFresh_imageNamed:@"icon_my_s"]];
}

+ (void)pushViewController:(UIViewController *)viewController animated:(BOOL)animated {
    [[FNFreshTabBarController shareInstance] pushViewController:viewController animated:animated];
}

- (void)backToTagetViewControllerWithType:(FNTabBarViewControllerType)type animated:(BOOL)animated {
    [self.navigationController popToRootViewControllerAnimated:animated];
    [self.navigationController dismissViewControllerAnimated:animated completion:nil];
    [self setSelectedIndex:type];
}

- (void)backToFNFreshTabBarControllerAnimated:(BOOL)animated{
    [self.navigationController popToRootViewControllerAnimated:animated];
    [self.navigationController dismissViewControllerAnimated:animated completion:nil];
}

- (UIViewController *)viewControllerInTabBarWithType:(FNTabBarViewControllerType)type {
    return [self.viewControllers safeObjectAtIndex:type];
}

- (void)switchTabBarWithParam:(NSDictionary*)param {
    
    NSInteger tabItemIndex = [[param objectForKey:@"tabItemIndex"] integerValue];
    if (tabItemIndex>=FNTabBarViewControllerTypeHome&&tabItemIndex<=FNTabBarViewControllerTypeMyAccount) {
        [self setSelectedIndex:tabItemIndex];
        switch (tabItemIndex) {
            case FNTabBarViewControllerTypeHome:{
                if ([self.selectedViewController isKindOfClass:[FNFreshMrFreshViewController class]]&&[[param objectForKey:@"ismembercenter"] integerValue]==1) {
                    // 线上跳门店首页
                    [self backToTagetViewControllerWithType:FNTabBarViewControllerTypeSpecialSell animated:YES];
                } else {
                    // 线下首页跳线上
                    [self backToTagetViewControllerWithType:FNTabBarViewControllerTypeHome animated:YES];
                }
                if ([self.selectedViewController isKindOfClass:[FNFreshTabBarController class]]&&[[param objectForKey:@"isrefresh"] integerValue]==1) {
                    [[NSNotificationCenter defaultCenter] postNotificationName:kFNFreshRefreshHomeDataNotification object:nil];
                }
            }
                break;
            default:
                break;
        }
    }
}

/**
 tabbar item 图标缩放动画
 */
static NSInteger lastIndex = -1;

- (void)tabBar:(UITabBar *)tabBar didSelectItem:(UITabBarItem *)item {
    NSUInteger index = [self.tabBar.items indexOfObject:item];
    if (self.selectedIndex == index && index == 0 && isFNFreshTarget) {
        return;
    }
    lastIndex = index;
    
    // 获取当前的tabbarButton
    UIView *currentTabbarBtn = [self.tabbarButtonArr safeObjectAtIndex:index];
    for (UIView *imageV in currentTabbarBtn.subviews) {
        if ([imageV isKindOfClass:NSClassFromString(@"UITabBarSwappableImageView")]) {
            if (isFNFreshTarget) {
                if (index == 0) {
                    [self.homeTabBarItemView selectedHomeAnimation];
                } else {
                    [self.homeTabBarItemView disSelectedHomeAnimation];
                }
            }
            imageV.transform = CGAffineTransformScale(imageV.transform, 0.83, 0.83);
            [UIView animateWithDuration:0.17f animations:^{
                imageV.transform = CGAffineTransformScale(imageV.transform, 1.4, 1.4);
            } completion:^(BOOL finished) {
                [UIView animateWithDuration:0.1f animations:^{
                    imageV.transform = CGAffineTransformIdentity;
                }];
            }];
        }
    }
}

- (BOOL)tabBarController:(UITabBarController *)tabBarController shouldSelectViewController:(UIViewController *)viewController {
    
    if (![viewController isKindOfClass:[FNFreshMrFreshViewController class]] && ![viewController isKindOfClass:[FNBaseNavigationController class]]) {
        self.homeVC.tabBarItem.title = [FNFreshMrFreshConstantCacheHandler shareInstance].homeStoreTitle;
    }
    if ([viewController isKindOfClass:[FNFreshMrFreshViewController class]]) {
        
        [[NSNotificationCenter defaultCenter] postNotificationName:kFNFreshScrollToTopNotification object:nil];
        [FNFreshAgent eventWithTrackDataPrameters: @{
            @"page_col":@"100069",
            @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
            @"track_type":@"2",
        }];
        
    } else if ([viewController isKindOfClass:[FNFreshDepartmentContainerController class]]) {
        
        [FNFreshAgent eventWithTrackDataPrameters: @{
            @"page_col":@"100070",
            @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
            @"track_type":@"2",
        }];
    } else if ([viewController isKindOfClass:[FNFreshOfflineBaseViewController class]]) {
        
        [FNFreshAgent eventWithTrackDataPrameters: @{
            @"page_col":@"114030",
            @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
            @"track_type":@"2",
        }];
    } else if ([viewController isKindOfClass:[FNFreshShopCartContainerController class]]) {
        
        [FNFreshAgent eventWithTrackDataPrameters: @{
            @"page_col":@"100071",
            @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
            @"track_type":@"2",
        }];
    } else if ([viewController isKindOfClass:[FNFreshUserCenterViewController class]]) {
        
        [FNFreshAgent eventWithTrackDataPrameters: @{
            @"page_col":@"100072",
            @"page_id":[[FNFreshUser shareInstance].deliveryCircleType isEqualToString:@"2"]?@"193":@"3",
            @"track_type":@"2",
        }];
    }
    return YES;
}

- (void)tabBarController:(UITabBarController *)tabBarController didSelectViewController:(UIViewController *)viewController {
    
    if ([viewController isKindOfClass:[FNFreshUserCenterViewController class]] ||
        (![viewController isKindOfClass:[FNFreshTabBarController class]] && self.tipType == 6)) {
        [self hiddenTabbarTipReminderView];
        [self postRemindTip:self.tipType];
        
        return;
    }
    if ([viewController isKindOfClass:[FNFreshShopCartContainerController class]]) {
        [self hiddenTabbarTipReminderView];
        FNFreshShopCartContainerController *shopVC = (FNFreshShopCartContainerController *)viewController;
        FNFreshShopCartViewController *vc = [shopVC shopCartVc];
        if (vc) {
            vc.isRecordFirst = YES;
        }
        return;
    }
    if (![viewController isKindOfClass:[FNFreshTabBarController class]] && self.tipType == 7) {
        [self hiddenTabbarTipReminderView];
        return;
    }
    if (!self.tipShow) {
        //底部导航tip接口请求
        [self requestMemCardTip];
    }
}

#pragma mark - 129首页容错页需要隐藏底部导航栏tabbar

- (void)hideTabbar:(BOOL)hide {
    self.tabBar.hidden = hide;
}

#pragma mark - shopCartCount

-(void)updateBadgeFinal:(NSString *)finalCount {
    NSString *text = finalCount;
    if (text.integerValue > 99) {
        text = @"99+";
        [self.tabBar showBadgeOnItemIndex:FNTabBarViewControllerTypeShoppingCart text:text];
    } else if (text.integerValue > 0) {
        [self.tabBar showBadgeOnItemIndex:FNTabBarViewControllerTypeShoppingCart text:text];
    } else {
        [self.tabBar hideBadgeOnItemIndex:FNTabBarViewControllerTypeShoppingCart];
    }
    _totalCount = finalCount;
    [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationProductNumber2Badge object:self userInfo:nil];
}

- (void)updateBadge2Zero {
    [self.tabBar hideBadgeOnItemIndex:FNTabBarViewControllerTypeShoppingCart];
    _totalCount = @"0";
    [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationProductNumber2Badge object:self userInfo:nil];
}

- (NSString *)getBadge {
    if ([_totalCount intValue] > 99) {
        return @"99+";
    }
    return _totalCount;
}

// 购物车 tab 的 badgeLabel
- (UILabel *)badgeLabel {
    for (NSNumber *num in self.tabBar.badgeLabels.allKeys) {
        if (num.integerValue == FNTabBarViewControllerTypeShoppingCart) {
            id obj = self.tabBar.badgeLabels[num];
            if ([obj isKindOfClass:UILabel.class]) {
                _badgeLabel = (UILabel *)obj;
            }
        }
    }
    return _badgeLabel;
}

#pragma mark - 堂食购物车数量
- (void)updateEatInStoreBadgeFinal:(NSString *)finalCount {
    _eatInStoretotalCount = finalCount;
    [[NSNotificationCenter defaultCenter] postNotificationName:kNotificationProductNumberInStoreBadge object:self userInfo:nil];
}

- (NSString *)getEatInStoreBadge {
    if ([_eatInStoretotalCount intValue] > 99) {
        return @"99+";
    }
    return _eatInStoretotalCount;
}

#pragma mark - 底部导航提醒tipType-1：新人礼包到账提醒，2：绑定会员卡提醒，3：优惠券过期提醒，4：新到券提醒，6：集点换购，7：膨胀券气泡提醒

+ (void)showTabbarRemindTipWithText:(NSString *)text tipType:(NSInteger)tipType {
    FNFreshTabBarController *tabBarController = [FNFreshTabBarController shareInstance];
    tabBarController.tipType = tipType;
    if (![tabBarController.navigationController.viewControllers.firstObject isKindOfClass:[FNFreshTabBarController class]] || [tabBarController.selectedViewController isMemberOfClass:[FNFreshUserCenterViewController class]]) {
        return;
    }
    
    if (tabBarController.tipShow) {
        return;
    }
    //    if (tipType == 7) {
    //        [[FNCacheManager shareMananger] setFirstOpenedForPage:FNMrFreshTaskBottomTipShowed];
    //    }
    tabBarController.tabbarTipRemindView = [tabBarController getBottomRemindTipView:tabBarController withText:text];
}

- (UIView *)getBottomRemindTipView:(UITabBarController *)tabBarController withText:(NSString *)text {
    FNFreshMrFreshCouponExpirationReminderView *view = [FNFreshMrFreshCouponExpirationReminderView couponExpirationReminderViewWithText:text];
    [tabBarController.view addSubview:view];
    [view mas_makeConstraints:^(MASConstraintMaker *make) {
        
        CGFloat x =  tabBarController.tabBar.frame.size.width / 5 / 2 - 27;
        make.right.equalTo(tabBarController.view).offset(-x);
        make.left.greaterThanOrEqualTo(tabBarController.view).offset(x);
        make.bottom.equalTo(tabBarController.view).offset(-(49 + (isHairIPhone ? 34 : 0) - 1));
    }];
    self.tipShow = YES;
    return view;
}

- (void)postRemindTip:(NSInteger)tipType {
    self.tipShow = NO;
    FNFreshFirstShowGiftParameterModel *parameter = [[FNFreshFirstShowGiftParameterModel alloc] init];
    parameter.tipType = tipType;
    [FNFreshMrFreshService requestMrFreshFirstShowGiftWithParameter:parameter success:^(FNFreshFirstShowGiftResponseModel *responseObject, BOOL isCache) {
    } failure:^(id responseObject, NSError *error) {
    }];
}

- (void)hiddenTabbarTipReminderView {
    self.tipShow = NO;
    [self.tabbarTipRemindView removeFromSuperview];
}

- (void)requestMemCardTip {
    if (self.homeVC.orderDisplayState == FNMrFreshOrderStateShowing){
        return;
    }
    // 容错页不展示气泡提示 & 定位兜底1001门店时，也不展示气泡
    if (self.homeVC.faultToleranceView || self.homeVC.isNewOrMiniError || [FNFreshUser shareInstance].faultTolerantStore) {
        return;
    }
    NSString *storeCode = nil;
    storeCode = [FNFreshUser shareInstance].shopId;
    // 成功回调
    void (^successBlock)(FNFreshMrFreshMemCardTipResponseModel *, BOOL) = ^(FNFreshMrFreshMemCardTipResponseModel *responseObject, BOOL isCache) {
        if (responseObject.orderDeliverModel != nil || responseObject.voucherExpireBottomTips != nil) {
            return;
        }else if (responseObject.tip.length > 0 && responseObject.memType == 1) { // 膨胀券提示信息
            [FNFreshTabBarController showTabbarRemindTipWithText:responseObject.tip tipType:7];
        } else if (responseObject.tip.length > 0 && responseObject.memType == 2) { // 新到券提示信息
            [FNFreshTabBarController showTabbarRemindTipWithText:responseObject.tip tipType:4];
        } else if (responseObject.tip.length > 0 && responseObject.memType == 3) { // 快过期券提示信息
            [FNFreshTabBarController showTabbarRemindTipWithText:responseObject.tip tipType:3];
        }
    };
    
    void (^failureBlock)(id, NSError *) = ^(id responseObject, NSError *error) {
        
    };
    FNFreshMrFreshMemCardTipParameterModel *params = [FNFreshMrFreshMemCardTipParameterModel new];
    params.storeCode = storeCode;
    params.lastStoreCode = self.lastVoucherExpireStoreCode;
    [FNFreshMrFreshService requestMemCardBindTipWithStoreCode:params
                                                      success:successBlock
                                                      failure:failureBlock];
}


#pragma mark - 152取消双首页后，维持单tabbarVC，故而baseRootVC中的一些跳转需要移到此处

- (void)postChangeHomeSuccessNotification {
    NSNotification *notification = [[NSNotification alloc] initWithName:kFNFreshRefreshHomeDataNotification object:@"success" userInfo:nil];
    [[NSNotificationCenter defaultCenter] postNotification:notification];
}

/// 首页手动选择展示线下门店
- (void)switchToOfflineHomeByManual {
    [self postChangeHomeSuccessNotification];
    [self.offlineVC chooseOffLine];
    [self backToTagetViewControllerWithType:FNTabBarViewControllerTypeSpecialSell animated:YES];
}

/// 手动q选择展示新店首页
- (void)switchToNewStoreHomeByManual {
    [self postChangeHomeSuccessNotification];
    [self.offlineVC chooseNewStore];
    [self backToTagetViewControllerWithType:FNTabBarViewControllerTypeSpecialSell animated:YES];
}

/// 首页手动选择展示mini门店
- (void)switchToMiniHome {
    [self postChangeHomeSuccessNotification];
    [self.offlineVC chooseMini];
    [self backToTagetViewControllerWithType:FNTabBarViewControllerTypeSpecialSell animated:YES];
}

- (void)chooseWXStore {
    [self postChangeHomeSuccessNotification];
    [self backToTagetViewControllerWithType:FNTabBarViewControllerTypeSpecialSell animated:YES];
}

#pragma mark - V153版本改为单首页后，线下切线上 当门店id变化了调用，通过通知监听

- (void)offlineToOnline {
    // 判断是否是从线下切回线上（线下门店切换后成功后才调次接口）
    if ([self.navigationController.visibleViewController isKindOfClass:[FNFreshStoreListViewController class]] || self.selectedIndex == FNTabBarViewControllerTypeSpecialSell) {
        
        FNFreshMrFreshSwitchLocationParameterModel *parameterModel = [[FNFreshMrFreshSwitchLocationParameterModel alloc] init];
        parameterModel.lastStoreId = [FNFreshUser shareInstance].shopId;
        
        [FNFreshMrFreshService requestSwitchHomeLocationWithParameter:parameterModel success:^(FNFreshGPSAddressResponseModel *responseObject, BOOL isCache) {
            
            FNFreshConsigneeModel *consigneeModel = responseObject.consigneeModel;
            FNFreshShopInfoModel *shopInfoModel = responseObject.shopInfo;
            
            [FNFreshUser shareInstance].homeInfoModel = responseObject.homeInfo;
            FNFreshGaoDeResponseModel *locationModel = responseObject.location;
            
            if (responseObject.homeInfo.locationWarn.length > 0) {
                [FNFreshUser shareInstance].addrWarn = responseObject.homeInfo.locationWarn;
            }
            
            if (responseObject.errorInfo.errorTip.length > 0) {
                [FNFreshUser shareInstance].errorTip = responseObject.errorInfo.errorTip;
            }
            
            if (responseObject.isLocationStore && locationModel) {
                [[FNFreshUser shareInstance] holdGaoDeInformationWithGaoDeCode:locationModel.gdDistrictCode latitude:locationModel.latitude longitude:locationModel.longitude poiName:locationModel.addrMap];
            }
            
            if (consigneeModel) {
                [FNFreshUser shareInstance].addressId = consigneeModel.addrId;
                [consigneeModel holdAddressLatitudeAndLongitude];
                [FNFreshUser shareInstance].deliveryAddress = [consigneeModel fullAddress:NO];
            }
            [FNFreshUser shareInstance].showType = responseObject.showType;
            [FNFreshUser shareInstance].faultTolerantPageInfo = responseObject.faultTolerantPageInfo;
            switch (responseObject.enterHomeType) {
                case FNFreshLocationOnlineStoreType: {
                    [[FNFreshUser shareInstance] holdShopInfomationWithShopInfoModel:shopInfoModel errorType:0 needFresh:false];
                }
                    break;
                    
                case FNFreshOnlineErrorType: {
                    if ([FNFreshUser shareInstance].storeType == 3) { // mini
                        // 首页展示mini容错页
                        NSString *addrMap = @"";
                        if (locationModel.addrMap.length > 0) {
                            addrMap = locationModel.addrMap;
                        }
                        [self.homeVC setOutOfScopeErrorWithAddress:addrMap isMini:YES];
                        [[NSNotificationCenter defaultCenter] postNotificationName:kFNFreshLocationOutOfScopeNotification object:nil];
                        return;
                    }
                    
                    // 首页展示容错页  这里需要判断下 shopInfo的storeStatus是否闭店，闭店展示容错页文案和mini相同
                    NSString *addrMap = @"";
                    if (locationModel.addrMap.length > 0) {
                        addrMap = locationModel.addrMap;
                    }
                    BOOL isClose = (shopInfoModel.storeStatus == 4 || shopInfoModel.storeStatus == 5);
                    [self.homeVC setOutOfScopeErrorWithAddress:addrMap isMini:isClose];
                    [[NSNotificationCenter defaultCenter] postNotificationName:kFNFreshLocationOutOfScopeNotification object:nil];
                }
                    break;
                    
                default:
                    break;
            }
        } failure:^(id responseObject, NSError *error) {
            NSNotification *notification = [[NSNotification alloc] initWithName:kFreshLocationNotification object:error userInfo:nil];
            [[NSNotificationCenter defaultCenter] postNotification:notification];
        }];
    }
}

#pragma mark - 更新 tab bar
- (void)shopDidChanged {
    //门店切换后请求接口 配置的tab icon
    [self.viewModel requestTabBottomInfo];
}

- (void)handleUpdateTabBar:(FNFreshTabBarUpdateType)type {
    switch (type) {
        case FNFreshTabBarUpdateTypeRefreshIcons:
            [self refreshTabBarIcons];
            break;
        case FNFreshTabBarUpdateTypeRefreshBg:
            [self modifyTabBarBgView];
            break;
        case FNFreshTabBarUpdateTypeNoData:
            self.tabBgImageView.hidden = true;
            break;
        default:
            break;
    }
}

- (void)refreshTabBarIcons {
    [self modifyItemImages];
    [self initializeShopCartTabIcon];
}

- (void)modifyItemImages {
    NSArray *items = self.viewModel.iconsResponse.iconArray;
    if (!items) return;
    
    for (NSUInteger i = 0; i < items.count; i++) {
        FNFreshTabBarIconModel *item = items[i];
        if (i == 0) {
            if (item.clickImgUrl.length > 0) {
                self.homeTabBarItemView.configTabImage = item.selectedIconImage;
            } else {
                self.homeTabBarItemView.configTabImage = NULL;
            }
            
            [self modifyControllerItemWithNormalImage:item.normalIconImage
                                        selectedImage:item.clickImgUrl.length > 0 ? nil : item.selectedIconImage
                                           index:i];
        } else {
            [self modifyControllerItemWithNormalImage:item.normalIconImage
                                        selectedImage:item.selectedIconImage
                                                index:i];
        }
    }
}

- (void)modifyTabBarBgView {
    self.tabBgImageView.hidden = true;
    if (self.viewModel.iconsResponse.bgImg) {
        self.tabBgImageView.hidden = false;
        self.tabBgImageView.image = self.viewModel.iconsResponse.bgImg;
        self.tabBgImageView.frame = CGRectMake(0, 0, self.tabBar.frame.size.width, self.tabBar.frame.size.height);
        [self removeTabBarTopLine];
    } else {
        [self changeTabBarLineColor:UIColor.whiteColor];
    }
}

- (void)modifyControllerItemWithNormalImage:(UIImage *)normalImage 
                              selectedImage:(UIImage *)selectedImage
                                 index:(NSUInteger)index {
    UIViewController *controller = [self.viewControllers safeObjectAtIndex:index];
    if (!controller) return;
    
    if (!normalImage) {
        normalImage = [UIImage fnFresh_imageNamed:[self.viewModel.defaultIconImageName safeObjectAtIndex:index]];
    }
    
    if (!selectedImage) {
        selectedImage = [UIImage fnFresh_imageNamed:[self.viewModel.defaultSelectIconImageName safeObjectAtIndex:index]];
    }
        
    controller.tabBarItem.image = [normalImage imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
    controller.tabBarItem.selectedImage = [selectedImage imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal];
}

- (FNFreshPasteboardRechargeHandler *)pasteboardHandler {
    if (!_pasteboardHandler) {
        _pasteboardHandler = [[FNFreshPasteboardRechargeHandler alloc] init];
    }
    return _pasteboardHandler;
}

@end
