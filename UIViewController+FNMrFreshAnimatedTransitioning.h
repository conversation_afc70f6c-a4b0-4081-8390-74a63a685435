//
//  UIViewController+FNMrFreshAnimatedTransitioning.h
//  FNFresh
//
//  Created by yo<PERSON><PERSON><PERSON> on 2017/4/1.
//  Copyright © 2017年 FeiNiu. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "FNFreshMrFreshCustomAnimateType.h"

@interface UIViewController (FNMrFreshAnimatedTransitioning)

@property (assign ,nonatomic, getter=isHasPresentedViewController) BOOL hasPresentedViewController;

- (void)fn_presentViewController:(UIViewController *)viewController customAnimateType:(FNFreshMrFreshCustomAnimateType)customAnimateType duration:(NSTimeInterval)duration alpha:(CGFloat)alpha handle:(void (^)(void))handle;

- (void)fn_presentViewController:(UIViewController *)viewController customAnimateType:(FNFreshMrFreshCustomAnimateType)customAnimateType needNavigation:(BOOL)need duration:(NSTimeInterval)duration alpha:(CGFloat)alpha handle:(void (^)(void))handle;

- (void)fn_presentViewController:(UIViewController *)viewController customAnimateType:(FNFreshMrFreshCustomAnimateType)customAnimateType needNavigation:(BOOL)need viewSize:(CGSize)viewSize duration:(NSTimeInterval)duration alpha:(CGFloat)alpha handle:(void (^)(void))handle;

- (void)fn_presentViewController:(UIViewController *)viewController customAnimateType:(FNFreshMrFreshCustomAnimateType)customAnimateType viewSize:(CGSize)viewSize duration:(NSTimeInterval)duration alpha:(CGFloat)alpha handle:(void(^)(void))handle;

@end
