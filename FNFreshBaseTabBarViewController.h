//
//  FNFreshBaseTabBarViewController.h
//  FNFresh
//
//  Created by EchoJuneMac-Pro on 2020/3/3.
//  Copyright © 2020 FeiNiu. All rights reserved.
//

#import <UIKit/UIKit.h>


@class FNMrFreshPictureModel;

@interface FNFreshBaseTabBarViewController : UITabBarController

// 子类方法
- (void)FNAddChildViewController:(UIViewController *)controller 
                           title:(NSString *)title
                           image:(UIImage *)image
                   selectedImage:(UIImage *)selectedImage;

- (UIViewController *)FNConfigureChildViewController:(UIViewController *)controller
                                               title:(NSString *)title
                                               image:(UIImage *)image
                                       selectedImage:(UIImage *)selectedImage;

- (void)pushViewController:(UIViewController *)viewController animated:(BOOL)animated;

- (void)backToHomeViewAnimated:(BOOL)flag;
/**
 给线上首页覆盖一个H5试图，暂时用于欧尚合并优鲜的H5公告页
 */
- (void)backToOnlineHomeViewAnimated:(BOOL)flag WithH5Link:(NSString *)link;

/// 获取 tabbar 购物车方法
- (void)initializeShopCartTabIcon;

- (void)changeTabBarLineColor:(UIColor *)color;
- (void)removeTabBarTopLine;

@end

