//
//  AppDelegate.m
//  FNFresh
//
//  Created by DC on 2017/2/7.
//  Copyright © 2017年 FeiNiu. All rights reserved.
//

#import "AppDelegate.h"
#import "FNAppDelegateService.h"
#import "FNEnvironmentConfiguration.h"
#import "FNFreshLocationLogic.h"
#import <AMapFoundationKit/AMapFoundationKit.h>
#import <AMapLocationKit/AMapLocationKit.h>
#import "SDImageWebPCoder.h"
#import <FNShareManager.h>
#import <FNPayManager.h>
#import "FNGeTuiPush.h"
#import "FNThirdLoginManager.h"
#import "FNFreshUrlRouter.h"
#import "FNRouterHelper.h"
#import "FNMediator+ConfigureModule.h"
#import "AppDelegate+Notification.h"
#import "FNBaseNavigationController.h"
#import "FNFreshPrivateViewController.h"
#import "FNBase64Encrypt.h"
#import "FNFreshGrowthHelper.h"
#import "FNMediator+FNFreshUserCenterModule.h"
#import "UIDevice+MTAgent.h"
#import <UMCommon/UMCommon.h>
#import "FNFreshShortcutManager.h"
#import "FNFreshWXOpenLaunchManager.h"
#import "FNMediator+FNSQShopModule.h"
#import <shopcore/shopcore.h>
#import "FNFreshPreviewModeViewController.h"
#import <QMapKit/QMapServices.h>
#import <QMapKit/QMSSearchServices.h>
#import "FNLocationManager.h"

#ifdef RELEASE
#import "FNVersionUpdate.h"
#endif

extern NSString *const FNMrFreshLastAppOpenTime;

@interface AppDelegate ()

@property (nonatomic, assign) BOOL isOpenWithSuspend;
@property (nonatomic, assign) BOOL isAppLaunching;
@property (nonatomic, strong) NSDate *lastBackgroundDate;

@end

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
    self.isOpenWithSuspend = NO;
    self.isAppLaunching = YES;
    
    [FNRouterHelper registRemoteSchemeArray:@[@"fnfresh"]];
    
    self.window = [[UIWindow alloc] initWithFrame:[[UIScreen mainScreen] bounds]];
    self.window.backgroundColor = [UIColor whiteColor];
    
    [self firstLunchMark];
    // 隐私协议判断
    [self checkPrivacyOptions:launchOptions];

    return ![launchOptions.allKeys containsObject:UIApplicationLaunchOptionsRemoteNotificationKey];
}

/// 设置初始化环境配置
- (void)initializeTheEnvironmentConfiguration {
    [FNEnvironmentConfiguration configDebugManagerEnvironmentWithDebugType:FNDomainTypeBeta
                                                            releaseType:FNDomainTypeBeta];
    [FNDebugManager requstEnvironment];
    
    [[FNBase64Encrypt shareInstance] setBase64EncryptOn:true];
    
    [[NSURLCache sharedURLCache] removeAllCachedResponses];
    
    [self configureWebViewUserAgent];
    [self configureSDWebImage];
    [self configureGaoDeApiKey];
    [self configureTencentApiKey];
    
    /// 配置第三方登陆/分享(FNShare所需要的微信分享,无需再次注册)
    [FNThirdLoginManager registerAppsWithWxID:kFreshWXAppID
                                     wxSecret:kFreshWXAppSecret
                              wxUniversalLink:kFreshWXUniversalLink];
    
    WS(weakSelf)
    [FNFreshWXOpenLaunchManager registerWXOpenLaunchCompletion:^(NSString * url) {
        [weakSelf handleRouterURL:[NSURL URLWithString:url]];
    }];

}

- (void)checkPrivacyOptions:(NSDictionary *)launchOptions 
{
    if ([FNFreshUtils shareInstance].showPrivacyDialog) {
        [self openPrivateViewControllerWithLaunchOptions:launchOptions];
    } else {
        [self initAppDelegateService:launchOptions isFromPriacy:NO];
    }
}

/// 隐私协议
- (void)openPrivateViewControllerWithLaunchOptions:(NSDictionary *)launchOptions
{
    WS(weakSelf)
    UIViewController *privateVC = [FNFreshPrivateViewController privateVCToPreviewMode:^{
        [weakSelf openPreviewModeViewControllerWithLaunchOptions:launchOptions];
    } completed:^{
        [weakSelf initAppDelegateService:launchOptions isFromPriacy:YES];
    }];
    
    self.window.rootViewController = privateVC;
    [self.window makeKeyAndVisible];
}

/// 浏览模式
- (void)openPreviewModeViewControllerWithLaunchOptions:(NSDictionary *)launchOptions
{
    WS(weakSelf)
    UIViewController *previewVC = [FNFreshPreviewModeViewController previewModeCompleted:^{
        [weakSelf initAppDelegateService:launchOptions isFromPriacy:YES];
    }];
    
    self.window.rootViewController = previewVC;
    [self.window makeKeyAndVisible];
}

- (void)initAppDelegateService:(NSDictionary *)launchOptions isFromPriacy:(BOOL) isFromPriacy {
#ifdef DEBUG
#else
    // 听云：首次启动开启网络、crash采集功能、用户体验分析。
    [NBSAppAgent setStartOption:NBSOption_Net|NBSOption_Crash|NBSOption_UI];
    NSString *udidString = [FNMTDevice shareInstance].fnUDID;
    if (udidString.length > 0) {
        [NBSAppAgent setUserIdentifier:udidString];
    }
#endif
    /**
     * 保障老用户更新第一次拿到这个新值，在进入app之前设为YES
     * 能进入app，说明已经同意过隐私协议
     */
    [FNFreshUtils shareInstance].didPrivacyDialogAgreeOnce = YES;
    // 设置环境
    [self initializeTheEnvironmentConfiguration];
    
    // 配置rootvc
    FNFreshBaseViewController *rootVC = [[FNMediator sharedInstance] ConfigureModule_Configure];
    FNBaseNavigationController *rootNav = [[FNBaseNavigationController alloc] initWithRootViewController:rootVC];
    [self.window setRootViewController:rootNav];
    [self.window makeKeyAndVisible];
    
    [self.appDelegateService prepareForFinishLaunching];
    
    // 注册APNS
    [self handleNotificationWithOptions:launchOptions];
    [self umengMobAnalytics];
    [self setupUpdateVersion];
    
    UIImageView.defaultPlaceholderColor = [UIColor whiteColor];
    /// 配置App Icon 3D Touch
    [FNFreshShortcutManager.shared setupShortcutItems];
}

- (void)umengMobAnalytics
{
#ifdef DEBUG
    return;
#else
    [UMConfigure initWithAppkey:FNFreshUmengKey channel:@"App Store"];
    [UMConfigure setLogEnabled:NO];
#endif
}

- (void)setupUpdateVersion
{
#ifdef RELEASE
    if (![FNFreshUtils shareInstance].updateAlertForTestClose) {
        FNVersionUpdate *update = [FNVersionUpdate shareInstance];
        NSString *preText = @"https://apkdownload.feiniugo.com/iOS/InHouse/";
        NSString *projectName = isFNFreshTarget?@"FNFresh":@"ACToGo";
        NSString *jsonURL = [NSString stringWithFormat:@"%@%@/IPAListConfig.json",preText,projectName];
        NSString *jumpAppPageURL = [NSString stringWithFormat:@"%@%@/downloadIPA.html",preText,projectName];
        update.jsonRequestURL = jsonURL;
        update.jumpLoadAppPageURL = jumpAppPageURL;
        update.loadAppURL = [NSString stringWithFormat:@"itms-services://?action=download-manifest&url=%@%@",preText,projectName];
        update.needAppendVersion = YES;
        update.compareKey = @"version";
        [update updateVersion];
    }
#endif
}

- (void)firstLunchMark{
    if(![[NSUserDefaults standardUserDefaults] boolForKey:@"firstLaunch"]){
        [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"firstLaunch"];
    }
    
    if (![[FNMediator sharedInstance] fnFreshUserCenterService_IsAllowGetPasteboardStr]) {
        //第一次启动默认开启剪切板权限
        [[FNMediator sharedInstance] fnFreshUserCenterService_SetIsAllowGetPasteboardStr: @"YES"];
    }
}

- (void)configureSDWebImage {
    SDImageWebPCoder *webPCoder = [SDImageWebPCoder sharedCoder];
    [[SDImageCodersManager sharedManager] addCoder:webPCoder];
    
    // 防图片盗刷
    SDWebImageDownloader *downloader = [SDWebImageManager sharedManager].imageLoader;
    [downloader setValue:@"https://membase-yxapp.feiniu.com" forHTTPHeaderField:@"Referer"];
    
    SDWebImageManager.sharedManager.cacheKeyFilter =[SDWebImageCacheKeyFilter cacheKeyFilterWithBlock:^NSString * _Nullable(NSURL * _Nonnull url) {
        
        if (!url.host || url.host.length == 0) {
            return url.absoluteString;
        }
        NSArray *array = [url.absoluteString componentsSeparatedByString:url.host];
        NSString *path = nil;
        if (array.count == 2) {
            path = array.lastObject;
        } else {
            path = [NSString stringWithFormat:@"%@?%@", url.relativePath ?: @"", url.query ?: @""];
        }
        return path;
    }];
}

- (void)configureGaoDeApiKey
{
    [[AMapServices sharedServices] setEnableHTTPS:!FNDebugManager.allowInvalidCertificates];
    
    if ([[[NSBundle mainBundle] bundleIdentifier] isEqualToString:@"com.feiniu.FNFresh.Internal"]) {
        [AMapServices sharedServices].apiKey = @"8a26366bbb1f1bfdf9d2b0826e6c9bbc";
    }else if ([[[NSBundle mainBundle] bundleIdentifier] isEqualToString:@"com.feiniu.actogo.internal"]) {
        [AMapServices sharedServices].apiKey = @"dfd0baf4ae53213a4b5691e37b09b494";
    }else if ([[[NSBundle mainBundle] bundleIdentifier] isEqualToString:@"com.feiniu.RTMarket"]) {
        [AMapServices sharedServices].apiKey = @"0ffb174258ec6b1b669f0ba3ffb35830";
    }else{
        [AMapServices sharedServices].apiKey = kFreshGaoDeApiKey;
    }
    [AMapLocationManager updatePrivacyAgree:AMapPrivacyAgreeStatusDidAgree];
    [AMapLocationManager updatePrivacyShow:AMapPrivacyShowStatusDidShow privacyInfo:AMapPrivacyInfoStatusDidContain];
}

- (void)configureTencentApiKey {
    
    if ([[[NSBundle mainBundle] bundleIdentifier] isEqualToString:@"com.feiniu.FNFresh"]) {
        [QMapServices sharedServices].APIKey = kFreshTencentApiKey;
        [[QMSSearchServices sharedServices] setApiKey:kFreshTencentApiKey];
        [[QMSSearchServices sharedServices] setSecretKey:kFreshTencentSecretKey];
        [FNLocationManager shared].tencentAPIKey = kFreshTencentApiKey;
        [FNLocationManager shared].tencentSecretKey = kFreshTencentSecretKey;
    }else{
        [QMapServices sharedServices].APIKey = kFreshTencentDebugApiKey;
        [[QMSSearchServices sharedServices] setApiKey:kFreshTencentDebugApiKey];
        [[QMSSearchServices sharedServices] setSecretKey:kFreshTencentDebugSecretKey];
        [FNLocationManager shared].tencentAPIKey = kFreshTencentDebugApiKey;
        [FNLocationManager shared].tencentSecretKey = kFreshTencentDebugSecretKey;
    }
    [[QMapServices sharedServices] setPrivacyAgreement:YES];
    
#ifdef DEBUG
    [[QMapServices sharedServices] setEnableDebugLog:YES];
#else
    [[QMapServices sharedServices] setEnableDebugLog:NO];
#endif
    
    [FNLocationManager shared].locationServiceType = FNLocationSDKTypeTencent;
    [FNLocationManager shared].reGeocodeServiceType = FNLocationSDKTypeTencent;
    [FNLocationManager shared].keywordSearchServiceType = FNLocationSDKTypeTencent;
}

#pragma mark - life cycle
- (BOOL)application:(UIApplication *)application continueUserActivity:(NSUserActivity *)userActivity restorationHandler:(void (^)(NSArray<id<UIUserActivityRestoring>> * _Nullable))restorationHandler {
    return [FNShareManager handleOpenUniversalLink:userActivity] || [FNThirdLoginManager handleOpenUniversalLink:userActivity] || [FNPayManager handleOpenUniversalLink:userActivity] || [FNFreshWXOpenLaunchManager handleOpenUniversalLink:userActivity] || [SQShopApi continueUserActivity:userActivity restorationHandler:restorationHandler];
}

- (void)applicationWillResignActive:(UIApplication *)application {
}

- (void)applicationDidEnterBackground:(UIApplication *)application {
    _isOpenWithSuspend = YES;
    _lastBackgroundDate = [NSDate date];
    [[FNFreshGrowthHelper shareInstance] growthStopTimer];
}

- (void)applicationWillEnterForeground:(UIApplication *)application {
    _isOpenWithNotification = NO;
    [[FNFreshGrowthHelper shareInstance] growthStartTimer];
}

- (void)applicationDidBecomeActive:(UIApplication *)application {
    if (![FNFreshUtils shareInstance].hasAgree) {
        return;
    }
    
    //  检查是否可以使用个推
    [FNGeTuiPush requestWhetherUseGetui];
    
    if (!_isOpenWithNotification&&_isOpenWithSuspend) {
        
        [self setAppBadgeNumber:0];
        
        NSDate *currentDate = [NSDate date];
        NSTimeInterval diff = [currentDate timeIntervalSinceDate:_lastBackgroundDate];
        if (diff >= 1800) { // 间隔30分钟走一次定位
            [FNFreshUser shareInstance].halfHourIntervalBecomeActive = YES;
        }
    } else {
        if (!self.isAppLaunching) {
            NSInteger badge = [UIApplication sharedApplication].applicationIconBadgeNumber;
            [FNGeTuiPush syncBadge:badge];
        }
    }
    // 粘贴板口令打开app自动充券，每次进入前台校验
    if (self.isAppLaunching) {
        self.isAppLaunching = NO;
        return;
    }
    [_appDelegateService configurePasteboard];
}

- (void)applicationWillTerminate:(UIApplication *)application {
    // Called when the application is about to terminate. Save data if appropriate. See also applicationDidEnterBackground:.
    //    [[NSUserDefaults standardUserDefaults] setBool:NO forKey:@"firstEnterLocation"];
    [FNFreshUser shareInstance].presentCity = nil;
    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"killServer"];
}

#pragma mark - openURL

- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options {
    return [self handleOpenURL:url];
}

- (BOOL)handleOpenURL:(NSURL *)url {
    [self handleChannelRef:url];
    [self handleRouterURL:url];
    return [FNShareManager handleOpenURL:url] || [FNThirdLoginManager handleOpenURL:url] || [FNPayManager handleOpenURL:url] || [FNFreshWXOpenLaunchManager handleOpenURL:url] || [SQShopApi handleOpenURL:url];
}

- (void)handleRouterURL:(NSURL *)url {
    NSString *shopId = [FNFreshUser shareInstance].shopId;
    if (shopId && ![shopId isEqualToString:@""]) {
        FNFreshADPageViewController *viewController = [FNFreshADPageViewController existInstance];
        if (viewController) {
            
            void(^block)(void) = ^{
                
                FNFreshUrlRouter *urlRouter = [[FNFreshUrlRouter alloc] init];
                [urlRouter jumpControllerWithRemoteURLString:[url absoluteString] completion:nil];
            };
            [viewController addBlockWith:block];
        } else {
            FNFreshUrlRouter *urlRouter = [[FNFreshUrlRouter alloc] init];
            [urlRouter jumpControllerWithRemoteURLString:[url absoluteString] completion:nil];
        }
    } else {
        [[FNFreshLocationLogic shareInstance] openPositionWithUrl:url payloadUrl:nil];
    }
}

#pragma mark - perform shortcut of App Icon
- (void)application:(UIApplication *)application performActionForShortcutItem:(UIApplicationShortcutItem *)shortcutItem completionHandler:(void (^)(BOOL))completionHandler {
    [FNFreshShortcutManager.shared handleShortcutItems:shortcutItem];
}

#pragma mark - background fetch
- (void)application:(UIApplication *)application performFetchWithCompletionHandler:(void (^)(UIBackgroundFetchResult))completionHandler
{
    completionHandler(UIBackgroundFetchResultNewData);
}

#pragma mark - private methods
- (FNAppDelegateService *)appDelegateService
{
    if (!_appDelegateService) {
        _appDelegateService = [[FNAppDelegateService alloc] init];
    }
    return _appDelegateService;
}

- (void)handleChannelRef:(NSURL *)url{
    NSString *query = [url.query stringByRemovingPercentEncoding];
    if (query.length > 0) {
        NSArray *pairs = [query componentsSeparatedByString:@"&"];
        NSMutableDictionary *paramDict = [[NSMutableDictionary alloc] initWithCapacity:5];
        for (NSString *pair in pairs) {
            NSArray *kv = [pair componentsSeparatedByString:@"="];
            if (kv.count == 2) {
                NSString *val = [[kv safeObjectAtIndex:1] stringByRemovingPercentEncoding];
                [paramDict safeSetObject:val forKey:[kv objectAtIndex:0]];
            }
        }
        NSString *ref = [paramDict safeObjectForKey:@"ref"];
        if (ref) {
            NSDate *date = [NSDate dateWithTimeIntervalSinceNow:0];
            NSString *refTimeString = [NSString stringWithFormat:@"%f",[date timeIntervalSince1970]];
            NSMutableDictionary *mutDic = [NSMutableDictionary dictionary];
            [mutDic setObject:refTimeString forKey:@"playloadRefTime"];
            [mutDic setObject:ref forKey:@"playloadRef"];
            [mutDic setObject:@"1" forKey:@"marketing_channel_code"];
            [[NSUserDefaults standardUserDefaults] setObject:mutDic forKey:@"playload_marking_code"];
            [[NSUserDefaults standardUserDefaults] synchronize];
        }
    }
}

- (void)configureWebViewUserAgent {

}

@end
